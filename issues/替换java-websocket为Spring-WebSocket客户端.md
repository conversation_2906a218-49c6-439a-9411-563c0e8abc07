# 替换java-websocket为Spring WebSocket客户端

## 任务背景
ruoyi-sol模块当前使用java-websocket库来监控Solana，需要改为使用已有的ruoyi-common-websocket模块实现。

## 执行方案
采用方案2：创建专用的WebSocket客户端适配器，使用Spring WebSocket客户端代替java-websocket。

## 执行计划
1. 移除java-websocket依赖
2. 创建Spring WebSocket客户端适配器
3. 重构SolanaWebSocketManagerImpl
4. 更新回调接口
5. 调整相关实现类
6. 移除java-websocket异常类
7. 测试验证

## 执行状态
- [x] 任务记录创建
- [x] 步骤1：依赖管理调整
- [x] 步骤2：创建Spring WebSocket客户端适配器
- [x] 步骤3：重构SolanaWebSocketManagerImpl
- [x] 步骤4：更新回调接口
- [x] 步骤5：调整相关实现类
- [x] 步骤6：移除java-websocket异常类（移除CloseInfo内部类）
- [x] 步骤7：测试验证（编译成功）

## 完成情况总结
✅ 成功移除了java-websocket依赖
✅ 创建了SpringWebSocketClientAdapter适配器类
✅ 重构了SolanaWebSocketManagerImpl使用Spring WebSocket
✅ 更新了接口定义，使用Spring的WebSocketSession和CloseStatus
✅ 修复了相关实现类的回调方法
✅ 移除了CloseInfo内部类，直接使用Spring的CloseStatus
✅ 项目编译成功

## 主要变更文件
1. ruoyi-modules/ruoyi-sol/pom.xml - 移除java-websocket依赖
2. SpringWebSocketClientAdapter.java - 新建Spring WebSocket客户端适配器
3. SolanaWebSocketManager.java - 更新接口定义
4. SolanaWebSocketManagerImpl.java - 重构实现类
5. SolanaAccountWatcherFacadeImpl.java - 调整回调方法 