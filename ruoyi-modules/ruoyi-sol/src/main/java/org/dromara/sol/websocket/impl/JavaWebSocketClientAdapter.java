package org.dromara.sol.websocket.impl;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Java-WebSocket客户端适配器
 * 使用org.java_websocket库，这是一个更简单、更可靠的WebSocket实现
 */
@Slf4j
public class JavaWebSocketClientAdapter {

    private WebSocketClient webSocketClient;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private CountDownLatch connectLatch;

    // 回调函数
    private Consumer<String> onOpenCallback;
    private Consumer<String> onMessageCallback;
    private Consumer<CloseStatus> onCloseCallback;
    private Consumer<Throwable> onErrorCallback;

    /**
     * 构造函数
     */
    public JavaWebSocketClientAdapter() {
        this.connectLatch = new CountDownLatch(1);
        log.info("创建Java-WebSocket客户端 - 简单可靠的实现");
    }

    /**
     * 连接到WebSocket服务器
     */
    public void connect(URI uri) {
        try {
            log.info("连接WebSocket: {}", uri);

            webSocketClient = new WebSocketClient(uri) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("WebSocket连接成功建立");
                    log.info("服务器状态: {} {}", handshake.getHttpStatus(), handshake.getHttpStatusMessage());
                    log.info("服务器Headers: {}", handshake.iterateHttpFields());
                    
                    isConnected.set(true);
                    connectLatch.countDown();

                    if (onOpenCallback != null) {
                        onOpenCallback.accept("连接成功");
                    }
                }

                @Override
                public void onMessage(String message) {
                    log.debug("收到WebSocket消息: {} 字节", message.length());
                    if (onMessageCallback != null) {
                        onMessageCallback.accept(message);
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("WebSocket连接关闭: [{}] {} (远程关闭: {})", code, reason, remote);
                    isConnected.set(false);
                    JavaWebSocketClientAdapter.this.connectLatch = new CountDownLatch(1);

                    if (onCloseCallback != null) {
                        CloseStatus closeStatus = new CloseStatus(code, reason);
                        onCloseCallback.accept(closeStatus);
                    }
                }

                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket传输错误: {}", ex.getMessage(), ex);
                    connectLatch.countDown();
                    if (onErrorCallback != null) {
                        onErrorCallback.accept(ex);
                    }
                }
            };

            // 设置连接超时
            webSocketClient.setConnectionLostTimeout(30);
            
            log.info("开始WebSocket连接...");
            webSocketClient.connect();

        } catch (Exception e) {
            log.error("连接WebSocket失败: {}", e.getMessage(), e);
            connectLatch.countDown();
            if (onErrorCallback != null) {
                onErrorCallback.accept(e);
            }
        }
    }

    /**
     * 等待连接建立
     */
    public boolean waitForConnection(long timeout, TimeUnit unit) throws InterruptedException {
        return connectLatch.await(timeout, unit);
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get() && webSocketClient != null && webSocketClient.isOpen();
    }

    /**
     * 发送消息
     */
    public boolean sendMessage(String message) {
        if (!isConnected() || webSocketClient == null) {
            log.warn("WebSocket未连接，无法发送消息");
            return false;
        }

        try {
            webSocketClient.send(message);
            return true;
        } catch (Exception e) {
            log.error("发送WebSocket消息失败", e);
            return false;
        }
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (webSocketClient != null && webSocketClient.isOpen()) {
            try {
                webSocketClient.close();
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败", e);
            }
        }
    }

    // 设置回调函数的方法
    public void setOnOpenCallback(Consumer<String> onOpenCallback) {
        this.onOpenCallback = onOpenCallback;
    }

    public void setOnMessageCallback(Consumer<String> onMessageCallback) {
        this.onMessageCallback = onMessageCallback;
    }

    public void setOnCloseCallback(Consumer<CloseStatus> onCloseCallback) {
        this.onCloseCallback = onCloseCallback;
    }

    public void setOnErrorCallback(Consumer<Throwable> onErrorCallback) {
        this.onErrorCallback = onErrorCallback;
    }
}
