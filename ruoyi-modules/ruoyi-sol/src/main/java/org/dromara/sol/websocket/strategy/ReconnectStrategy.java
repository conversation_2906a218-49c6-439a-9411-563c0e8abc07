package org.dromara.sol.websocket.strategy;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 重连策略管理器
 * 负责管理WebSocket的重连逻辑
 */
@Slf4j
@Component
public class ReconnectStrategy {
    
    private final WebSocketConfig config;
    private final ScheduledExecutorService scheduler;
    private final AtomicInteger attemptCount = new AtomicInteger(0);
    private final AtomicLong lastReconnectTime = new AtomicLong(0);
    
    public ReconnectStrategy(WebSocketConfig config) {
        this.config = config;
        this.scheduler = new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r, "websocket-reconnect");
            thread.setDaemon(true);
            return thread;
        });
    }
    
    /**
     * 安排重连任务
     * 
     * @param reconnectTask 重连任务
     * @return 是否成功安排重连
     */
    public boolean scheduleReconnect(Runnable reconnectTask) {
        if (!config.isAutoReconnect()) {
            log.debug("自动重连已禁用，跳过重连");
            return false;
        }
        
        int currentAttempt = attemptCount.incrementAndGet();
        
        // 检查是否超过最大重连次数
        if (currentAttempt > config.getMaxReconnectAttempts()) {
            if (isInCooldownPeriod()) {
                log.info("仍在冷却期内，跳过重连");
                return false;
            }
            
            // 重置计数器，进入冷却期
            log.info("达到最大重连次数({}次)，进入冷却期", config.getMaxReconnectAttempts());
            resetAttemptCount();
            lastReconnectTime.set(System.currentTimeMillis());
            
            // 安排冷却期后的重连
            scheduler.schedule(() -> {
                log.info("冷却期结束，重新开始重连");
                scheduleReconnect(reconnectTask);
            }, config.getReconnectCooldownPeriod(), TimeUnit.MILLISECONDS);
            
            return true;
        }
        
        // 计算延迟时间（指数退避）
        long delay = calculateDelay(currentAttempt);
        
        log.info("安排第{}次重连，延迟{}ms", currentAttempt, delay);
        
        scheduler.schedule(() -> {
            try {
                reconnectTask.run();
            } catch (Exception e) {
                log.error("重连任务执行失败", e);
                // 继续安排下一次重连
                scheduleReconnect(reconnectTask);
            }
        }, delay, TimeUnit.MILLISECONDS);
        
        return true;
    }
    
    /**
     * 重置重连计数器（连接成功时调用）
     */
    public void resetAttemptCount() {
        attemptCount.set(0);
    }
    
    /**
     * 获取当前重连次数
     */
    public int getCurrentAttemptCount() {
        return attemptCount.get();
    }
    
    /**
     * 检查是否在冷却期内
     */
    private boolean isInCooldownPeriod() {
        long now = System.currentTimeMillis();
        long lastTime = lastReconnectTime.get();
        return lastTime > 0 && (now - lastTime) < config.getReconnectCooldownPeriod();
    }
    
    /**
     * 计算重连延迟时间（指数退避算法）
     */
    private long calculateDelay(int attempt) {
        long delay = config.getInitialReconnectDelay() * (1L << (attempt - 1));
        return Math.min(delay, config.getMaxReconnectDelay());
    }
    
    /**
     * 关闭重连策略管理器
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
