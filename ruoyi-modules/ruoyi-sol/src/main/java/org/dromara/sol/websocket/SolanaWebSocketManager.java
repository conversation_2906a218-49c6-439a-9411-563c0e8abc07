package org.dromara.sol.websocket;

import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * Solana WebSocket连接管理器
 * 负责WebSocket连接的建立、维护和重连逻辑
 */
public interface SolanaWebSocketManager {

    /**
     * 初始化WebSocket连接
     */
    void initializeWebSocketClient();

    /**
     * 获取WebSocket连接状态
     *
     * @return 连接状态，true表示已连接，false表示未连接
     */
    boolean isConnected();

    /**
     * 发送WebSocket消息
     *
     * @param message 要发送的消息
     * @return 是否发送成功
     */
    boolean sendMessage(String message);

    /**
     * 关闭WebSocket连接
     */
    void closeConnection();

    /**
     * 设置连接打开回调
     *
     * @param onOpenCallback 连接打开时的回调函数
     */
    void setOnOpenCallback(Consumer<WebSocketSession> onOpenCallback);

    /**
     * 设置消息接收回调
     *
     * @param onMessageCallback 接收消息时的回调函数
     */
    void setOnMessageCallback(Consumer<String> onMessageCallback);

    /**
     * 设置连接关闭回调
     *
     * @param onCloseCallback 连接关闭时的回调函数
     */
    void setOnCloseCallback(Consumer<CloseStatus> onCloseCallback);

    /**
     * 清理资源
     */
    void cleanup();

    /**
     * 等待连接建立，最多等待指定的时间
     *
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 是否成功连接
     * @throws InterruptedException 如果等待过程被中断
     */
    boolean waitForConnection(long timeout, TimeUnit unit) throws InterruptedException;


}
