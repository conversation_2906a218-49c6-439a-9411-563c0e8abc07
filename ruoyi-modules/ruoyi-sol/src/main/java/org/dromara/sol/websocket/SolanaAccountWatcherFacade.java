package org.dromara.sol.websocket;

import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.event.WalletMonitorEvent;
/**
 * Solana账户监控器外观
 * 整合WebSocket连接管理、地址订阅、交易处理和交易扫描等组件
 * 提供统一的接口
 */
public interface SolanaAccountWatcherFacade {

    /**
     * 获取WebSocket连接状态
     *
     * @return 连接状态，true表示已连接，false表示未连接
     */
    boolean isConnected();

    /**
     * 订阅指定地址的交易日志
     *
     * @param address  要监控的地址
     * @param id       钱包ID
     * @param coinType 币种类型 (USDT/USDC)
     * @return 是否成功发送订阅请求
     */
    boolean subscribeAddress(String address, Long id, SolCoinType coinType);

    /**
     * 添加监控地址
     * 1. 添加到Redis
     * 2. 发送WebSocket订阅请求
     *
     * @param wallet 钱包信息
     */
    void addSubjectMonitorAddress(MetaSolanaCstaddressinfoVo wallet);

    /**
     * 重新订阅所有监控地址
     * WebSocket重连后调用
     */
    void resubscribeAllAddresses();

    /**
     * 扫描所有监控地址的遗漏交易
     * 用于服务启动或长时间断连后重连时的全量检查
     */
    void scanAllAddressesForMissedTransactions();

    /**
     * 初始化WebSocket连接
     */
    void initializeWebSocketClient();

    /**
     * 处理钱包监控事件
     *
     * @param event 钱包监控事件
     */
    void handleWalletMonitorEvent(WalletMonitorEvent event);
}
