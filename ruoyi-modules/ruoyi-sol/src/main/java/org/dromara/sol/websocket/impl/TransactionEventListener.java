package org.dromara.sol.websocket.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.event.TransactionEvent;
import org.dromara.sol.manager.SolTransactionManager;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 交易事件监听器
 * 用于监听交易事件并调用相应的处理逻辑
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionEventListener {

    private final SolTransactionManager solTransactionManager;

    /**
     * 处理交易事件
     *
     * @param event 交易事件
     */
    @EventListener
    public void handleTransactionEvent(TransactionEvent event) {
        String signature = event.getSignature();
        String address = event.getAddress();

        log.info("收到交易事件：签名={}, 关联地址={}", signature, address);

        if (event.hasAddress()) {
            solTransactionManager.callTransactionBySignature(signature, address);
        } else {
            solTransactionManager.callTransactionBySignature(signature);
        }
    }
}
