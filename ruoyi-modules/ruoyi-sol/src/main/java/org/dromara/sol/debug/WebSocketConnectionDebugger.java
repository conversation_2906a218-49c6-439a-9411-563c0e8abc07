package org.dromara.sol.debug;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.impl.SpringWebSocketClientAdapter;
import org.springframework.web.socket.CloseStatus;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * WebSocket连接调试器
 * 用于诊断QuickNode WebSocket连接问题
 */
@Slf4j
public class WebSocketConnectionDebugger {

    private static final String QUICKNODE_WSS_URL = "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/";
    private static final String SOLANA_OFFICIAL_WSS_URL = "wss://api.devnet.solana.com/";

    public static void main(String[] args) {
        WebSocketConnectionDebugger debugger = new WebSocketConnectionDebugger();
        
        log.info("=== WebSocket连接调试器启动 ===");
        
        // 测试QuickNode连接
        log.info("\n" + "=".repeat(60));
        log.info("测试 QuickNode WebSocket 连接");
        log.info("=".repeat(60));
        debugger.testConnection(QUICKNODE_WSS_URL, "QuickNode");
        
        // 等待一段时间
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试Solana官方连接
        log.info("\n" + "=".repeat(60));
        log.info("测试 Solana官方 WebSocket 连接");
        log.info("=".repeat(60));
        debugger.testConnection(SOLANA_OFFICIAL_WSS_URL, "Solana官方");
        
        log.info("\n=== 调试完成 ===");
    }

    public void testConnection(String wsUrl, String provider) {
        SpringWebSocketClientAdapter client = new SpringWebSocketClientAdapter();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicBoolean errorOccurred = new AtomicBoolean(false);
        StringBuilder errorDetails = new StringBuilder();

        // 设置回调函数
        client.setOnOpenCallback(session -> {
            log.info("✅ {} WebSocket连接成功建立！", provider);
            log.info("会话ID: {}", session.getId());
            log.info("远程地址: {}", session.getRemoteAddress());
            log.info("连接状态: {}", session.isOpen() ? "已打开" : "已关闭");
            connected.set(true);
            connectionLatch.countDown();
        });

        client.setOnErrorCallback(throwable -> {
            log.error("❌ {} WebSocket连接错误: {}", provider, throwable.getMessage());
            log.error("错误类型: {}", throwable.getClass().getSimpleName());
            if (throwable.getCause() != null) {
                log.error("根本原因: {}", throwable.getCause().getMessage());
            }
            errorOccurred.set(true);
            errorDetails.append(throwable.getMessage());
            connectionLatch.countDown();
        });

        client.setOnCloseCallback(closeStatus -> {
            log.info("🔌 {} WebSocket连接关闭: [{}] {}", provider, closeStatus.getCode(), closeStatus.getReason());
        });

        client.setOnMessageCallback(message -> {
            log.info("📨 收到{}消息: {}", provider, message.length() > 100 ? 
                message.substring(0, 100) + "..." : message);
        });

        try {
            log.info("🔄 正在连接到 {} WebSocket: {}", provider, wsUrl);
            URI uri = new URI(wsUrl);
            
            // 记录连接详情
            log.info("URI详情:");
            log.info("  - Scheme: {}", uri.getScheme());
            log.info("  - Host: {}", uri.getHost());
            log.info("  - Port: {}", uri.getPort() == -1 ? "默认(443)" : uri.getPort());
            log.info("  - Path: {}", uri.getPath().isEmpty() ? "/" : uri.getPath());

            long startTime = System.currentTimeMillis();
            client.connect(uri);

            // 等待连接结果
            boolean connectionResult = connectionLatch.await(30, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("连接尝试耗时: {} 毫秒", duration);

            if (connectionResult && connected.get()) {
                log.info("✅ {} 连接测试成功！", provider);
                
                // 测试发送消息
                testSendMessage(client, provider);
                
                // 等待一段时间接收消息
                Thread.sleep(3000);
                
            } else {
                log.error("❌ {} 连接测试失败！", provider);
                
                if (errorOccurred.get()) {
                    log.error("错误详情: {}", errorDetails.toString());
                } else {
                    log.error("连接超时 - 30秒内未能建立连接");
                }
                
                // 提供故障排除建议
                provideTroubleshootingAdvice(provider, wsUrl, errorDetails.toString());
            }

        } catch (Exception e) {
            log.error("❌ {} 连接测试异常: {}", provider, e.getMessage(), e);
        } finally {
            // 清理连接
            try {
                client.close();
                log.info("🧹 {} 连接已清理", provider);
            } catch (Exception e) {
                log.warn("清理连接时发生错误", e);
            }
        }
    }

    private void testSendMessage(SpringWebSocketClientAdapter client, String provider) {
        try {
            log.info("📤 测试发送消息到 {}", provider);
            
            // 发送一个简单的Solana RPC请求
            String testMessage = """
                {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                """;
            
            boolean sent = client.sendMessage(testMessage);
            if (sent) {
                log.info("✅ 消息发送成功到 {}", provider);
            } else {
                log.warn("⚠️ 消息发送失败到 {}", provider);
            }
            
        } catch (Exception e) {
            log.error("发送测试消息时发生错误", e);
        }
    }

    private void provideTroubleshootingAdvice(String provider, String wsUrl, String errorMessage) {
        log.info("\n🔧 {} 故障排除建议:", provider);
        log.info("1. 检查网络连接");
        log.info("2. 验证WebSocket URL: {}", wsUrl);
        log.info("3. 检查防火墙设置");
        log.info("4. 验证SSL/TLS证书");
        
        if (provider.contains("QuickNode")) {
            log.info("5. 检查QuickNode仪表板中的端点状态");
            log.info("6. 验证QuickNode账户是否有效");
            log.info("7. 检查是否需要API密钥认证");
            log.info("8. 尝试在QuickNode仪表板中重新生成端点");
        }
        
        // 根据错误信息提供具体建议
        if (errorMessage.contains("timeout") || errorMessage.contains("超时")) {
            log.info("9. 网络超时 - 检查网络连接速度和稳定性");
        }
        
        if (errorMessage.contains("SSL") || errorMessage.contains("certificate")) {
            log.info("9. SSL证书问题 - 检查系统时间和证书信任设置");
        }
        
        if (errorMessage.contains("403") || errorMessage.contains("Forbidden")) {
            log.info("9. 访问被拒绝 - 检查API密钥或端点权限");
        }
        
        if (errorMessage.contains("404") || errorMessage.contains("Not Found")) {
            log.info("9. 端点不存在 - 验证WebSocket URL是否正确");
        }
        
        log.info("10. 尝试使用curl测试连接:");
        log.info("    curl -i -N -H \"Connection: Upgrade\" -H \"Upgrade: websocket\" \\");
        log.info("         -H \"Sec-WebSocket-Version: 13\" -H \"Sec-WebSocket-Key: test\" \\");
        log.info("         {}", wsUrl.replace("wss://", "https://"));
    }
}
