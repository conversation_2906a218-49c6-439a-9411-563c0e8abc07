package org.dromara.sol.debug;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.impl.JavaWebSocketClientAdapter;
import org.dromara.sol.websocket.impl.SpringWebSocketClientAdapter;
import org.springframework.web.socket.CloseStatus;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * WebSocket实现对比测试
 * 比较Spring WebSocket和Java-WebSocket两种实现
 */
@Slf4j
public class WebSocketImplementationComparison {

    private static final String QUICKNODE_WSS_URL = "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/";
    private static final String SOLANA_OFFICIAL_WSS_URL = "wss://api.devnet.solana.com/";

    public static void main(String[] args) {
        WebSocketImplementationComparison comparison = new WebSocketImplementationComparison();
        
        log.info("=== WebSocket实现对比测试 ===");
        
        // 测试Java-WebSocket实现
        log.info("\n" + "=".repeat(60));
        log.info("测试 Java-WebSocket 实现 (QuickNode)");
        log.info("=".repeat(60));
        comparison.testJavaWebSocketImplementation(QUICKNODE_WSS_URL, "QuickNode");
        
        // 等待一段时间
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试Spring WebSocket实现
        log.info("\n" + "=".repeat(60));
        log.info("测试 Spring WebSocket 实现 (QuickNode)");
        log.info("=".repeat(60));
        comparison.testSpringWebSocketImplementation(QUICKNODE_WSS_URL, "QuickNode");
        
        log.info("\n=== 对比测试完成 ===");
    }

    public void testJavaWebSocketImplementation(String wsUrl, String provider) {
        JavaWebSocketClientAdapter client = new JavaWebSocketClientAdapter();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicBoolean errorOccurred = new AtomicBoolean(false);
        StringBuilder errorDetails = new StringBuilder();

        // 设置回调函数
        client.setOnOpenCallback(message -> {
            log.info("✅ {} Java-WebSocket连接成功建立！", provider);
            connected.set(true);
            connectionLatch.countDown();
        });

        client.setOnErrorCallback(throwable -> {
            log.error("❌ {} Java-WebSocket连接错误: {}", provider, throwable.getMessage());
            errorOccurred.set(true);
            errorDetails.append(throwable.getMessage());
            connectionLatch.countDown();
        });

        client.setOnCloseCallback(closeStatus -> {
            log.info("🔌 {} Java-WebSocket连接关闭: [{}] {}", provider, closeStatus.getCode(), closeStatus.getReason());
        });

        client.setOnMessageCallback(message -> {
            log.info("📨 收到{}消息: {}", provider, message.length() > 100 ? 
                message.substring(0, 100) + "..." : message);
        });

        try {
            log.info("🔄 正在使用Java-WebSocket连接到: {}", wsUrl);
            URI uri = new URI(wsUrl);

            long startTime = System.currentTimeMillis();
            client.connect(uri);

            // 等待连接结果
            boolean connectionResult = connectionLatch.await(30, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("Java-WebSocket连接尝试耗时: {} 毫秒", duration);

            if (connectionResult && connected.get()) {
                log.info("✅ {} Java-WebSocket连接测试成功！", provider);
                
                // 测试发送消息
                testSendMessage(client, provider, "Java-WebSocket");
                
                // 等待一段时间接收消息
                Thread.sleep(3000);
                
            } else {
                log.error("❌ {} Java-WebSocket连接测试失败！", provider);
                
                if (errorOccurred.get()) {
                    log.error("错误详情: {}", errorDetails.toString());
                } else {
                    log.error("连接超时 - 30秒内未能建立连接");
                }
            }

        } catch (Exception e) {
            log.error("❌ {} Java-WebSocket连接测试异常: {}", provider, e.getMessage(), e);
        } finally {
            // 清理连接
            try {
                client.close();
                log.info("🧹 {} Java-WebSocket连接已清理", provider);
            } catch (Exception e) {
                log.warn("清理Java-WebSocket连接时发生错误", e);
            }
        }
    }

    public void testSpringWebSocketImplementation(String wsUrl, String provider) {
        SpringWebSocketClientAdapter client = new SpringWebSocketClientAdapter();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicBoolean errorOccurred = new AtomicBoolean(false);
        StringBuilder errorDetails = new StringBuilder();

        // 设置回调函数
        client.setOnOpenCallback(session -> {
            log.info("✅ {} Spring WebSocket连接成功建立！", provider);
            connected.set(true);
            connectionLatch.countDown();
        });

        client.setOnErrorCallback(throwable -> {
            log.error("❌ {} Spring WebSocket连接错误: {}", provider, throwable.getMessage());
            errorOccurred.set(true);
            errorDetails.append(throwable.getMessage());
            connectionLatch.countDown();
        });

        client.setOnCloseCallback(closeStatus -> {
            log.info("🔌 {} Spring WebSocket连接关闭: [{}] {}", provider, closeStatus.getCode(), closeStatus.getReason());
        });

        client.setOnMessageCallback(message -> {
            log.info("📨 收到{}消息: {}", provider, message.length() > 100 ? 
                message.substring(0, 100) + "..." : message);
        });

        try {
            log.info("🔄 正在使用Spring WebSocket连接到: {}", wsUrl);
            URI uri = new URI(wsUrl);

            long startTime = System.currentTimeMillis();
            client.connect(uri);

            // 等待连接结果
            boolean connectionResult = connectionLatch.await(30, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("Spring WebSocket连接尝试耗时: {} 毫秒", duration);

            if (connectionResult && connected.get()) {
                log.info("✅ {} Spring WebSocket连接测试成功！", provider);
                
                // 测试发送消息
                testSendMessage(client, provider, "Spring WebSocket");
                
                // 等待一段时间接收消息
                Thread.sleep(3000);
                
            } else {
                log.error("❌ {} Spring WebSocket连接测试失败！", provider);
                
                if (errorOccurred.get()) {
                    log.error("错误详情: {}", errorDetails.toString());
                } else {
                    log.error("连接超时 - 30秒内未能建立连接");
                }
            }

        } catch (Exception e) {
            log.error("❌ {} Spring WebSocket连接测试异常: {}", provider, e.getMessage(), e);
        } finally {
            // 清理连接
            try {
                client.close();
                log.info("🧹 {} Spring WebSocket连接已清理", provider);
            } catch (Exception e) {
                log.warn("清理Spring WebSocket连接时发生错误", e);
            }
        }
    }

    private void testSendMessage(Object client, String provider, String implementation) {
        try {
            log.info("📤 测试发送消息到 {} ({})", provider, implementation);
            
            String testMessage = """
                {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                """;
            
            boolean sent = false;
            if (client instanceof JavaWebSocketClientAdapter) {
                sent = ((JavaWebSocketClientAdapter) client).sendMessage(testMessage);
            } else if (client instanceof SpringWebSocketClientAdapter) {
                sent = ((SpringWebSocketClientAdapter) client).sendMessage(testMessage);
            }
            
            if (sent) {
                log.info("✅ 消息发送成功到 {} ({})", provider, implementation);
            } else {
                log.warn("⚠️ 消息发送失败到 {} ({})", provider, implementation);
            }
            
        } catch (Exception e) {
            log.error("发送测试消息时发生错误", e);
        }
    }
}
