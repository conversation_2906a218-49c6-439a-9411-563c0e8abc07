package org.dromara.sol.websocket.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.*;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.client.WebSocketClient;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Spring WebSocket客户端适配器 - 修复QuickNode连接问题
 * 添加必要的Headers以确保与QuickNode兼容
 */
@Slf4j
public class SpringWebSocketClientAdapter {

    private final WebSocketClient webSocketClient;
    private WebSocketSession webSocketSession;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private CountDownLatch connectLatch;

    // 回调函数
    private Consumer<WebSocketSession> onOpenCallback;
    private Consumer<String> onMessageCallback;
    private Consumer<CloseStatus> onCloseCallback;
    private Consumer<Throwable> onErrorCallback;

    /**
     * 构造函数 - 使用默认配置，专门为QuickNode优化
     */
    public SpringWebSocketClientAdapter() {
        this.webSocketClient = new StandardWebSocketClient();
        this.connectLatch = new CountDownLatch(1);
        log.info("创建Spring WebSocket客户端 - QuickNode兼容配置");
    }

    /**
     * 连接到WebSocket服务器 - 添加QuickNode必需的Headers
     */
    public void connect(URI uri) {
        try {
            log.info("连接WebSocket: {}", uri);

            // 创建WebSocket Headers，添加QuickNode需要的Headers
            WebSocketHttpHeaders headers = new WebSocketHttpHeaders();
            // QuickNode文档建议的Headers
            headers.add("User-Agent", "solana-client");
            headers.add("Origin", "https://localhost");
            // 保持连接活跃
            headers.add("Connection", "keep-alive");
            headers.add("Pragma", "no-cache");
            headers.add("Cache-Control", "no-cache");

            WebSocketHandler handler = new WebSocketHandler() {
                @Override
                public void afterConnectionEstablished(WebSocketSession session) throws Exception {
                    log.info("WebSocket连接成功建立 - QuickNode连接正常");
                    SpringWebSocketClientAdapter.this.webSocketSession = session;
                    isConnected.set(true);
                    connectLatch.countDown();

                    if (onOpenCallback != null) {
                        onOpenCallback.accept(session);
                    }
                }

                @Override
                public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
                    if (message instanceof TextMessage) {
                        String payload = ((TextMessage) message).getPayload();
                        log.debug("收到WebSocket消息: {} 字节", payload.length());
                        if (onMessageCallback != null) {
                            onMessageCallback.accept(payload);
                        }
                    }
                }

                @Override
                public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
                    log.error("WebSocket传输错误: {}", exception.getMessage(), exception);
                    connectLatch.countDown();
                    if (onErrorCallback != null) {
                        onErrorCallback.accept(exception);
                    }
                }

                @Override
                public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
                    log.info("WebSocket连接关闭: {}", closeStatus);
                    isConnected.set(false);
                    SpringWebSocketClientAdapter.this.webSocketSession = null;
                    SpringWebSocketClientAdapter.this.connectLatch = new CountDownLatch(1);

                    if (onCloseCallback != null) {
                        onCloseCallback.accept(closeStatus);
                    }
                }

                @Override
                public boolean supportsPartialMessages() {
                    return false;
                }
            };

            // 使用Headers连接 - 这是关键修复
            log.info("开始WebSocket连接，使用QuickNode兼容Headers...");
            webSocketClient.execute(handler, headers, uri);

        } catch (Exception e) {
            log.error("连接WebSocket失败: {}", e.getMessage(), e);
            connectLatch.countDown();
            if (onErrorCallback != null) {
                onErrorCallback.accept(e);
            }
        }
    }

    /**
     * 等待连接建立
     */
    public boolean waitForConnection(long timeout, TimeUnit unit) throws InterruptedException {
        return connectLatch.await(timeout, unit);
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get() && webSocketSession != null && webSocketSession.isOpen();
    }

    /**
     * 发送消息
     */
    public boolean sendMessage(String message) {
        if (!isConnected() || webSocketSession == null) {
            log.warn("WebSocket未连接，无法发送消息");
            return false;
        }

        try {
            webSocketSession.sendMessage(new TextMessage(message));
            return true;
        } catch (Exception e) {
            log.error("发送WebSocket消息失败", e);
            return false;
        }
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (webSocketSession != null && webSocketSession.isOpen()) {
            try {
                webSocketSession.close();
            } catch (Exception e) {
                log.error("关闭WebSocket连接失败", e);
            }
        }
    }

    // 设置回调函数的方法
    public void setOnOpenCallback(Consumer<WebSocketSession> onOpenCallback) {
        this.onOpenCallback = onOpenCallback;
    }

    public void setOnMessageCallback(Consumer<String> onMessageCallback) {
        this.onMessageCallback = onMessageCallback;
    }

    public void setOnCloseCallback(Consumer<CloseStatus> onCloseCallback) {
        this.onCloseCallback = onCloseCallback;
    }

    public void setOnErrorCallback(Consumer<Throwable> onErrorCallback) {
        this.onErrorCallback = onErrorCallback;
    }
}
