package org.dromara.sol.websocket.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.event.TransactionEvent;
import org.dromara.sol.event.WebSocketMessageEvent;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.manager.SolTransactionManager;
import org.dromara.sol.websocket.SolanaAddressSubscriber;
import org.dromara.sol.websocket.SolanaTransactionProcessor;
import org.dromara.sol.websocket.WebSocketMessageListener;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Solana交易处理器实现
 * 负责处理接收到的交易消息
 * 支持所有在SolCoinType枚举中定义的币种类型
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaTransactionProcessorImpl implements SolanaTransactionProcessor, WebSocketMessageListener {

    private final SolMonitorManager solMonitorManager;
    private final SolTransactionManager solTransactionManager;
    private final SolanaAddressSubscriber solanaAddressSubscriber;
    private final ApplicationEventPublisher eventPublisher;

    // ObjectMapper实例，避免重复创建
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理WebSocket消息事件
     *
     * @param event WebSocket消息事件
     */
    @Override
    @EventListener
    public void onWebSocketMessage(WebSocketMessageEvent event) {
        handleMessage(event.getMessage());
    }

    @Override
    public void handleMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(message);

            // 检查是否是订阅确认消息
            if (rootNode.has("result")) {
                handleSubscriptionConfirmation(rootNode);
                return;
            }

            // 处理交易消息
            handleTransactionMessage(rootNode);

        } catch (JsonProcessingException e) {
            log.error("解析JSON消息失败: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理WebSocket消息时发生异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleSubscriptionConfirmation(JsonNode rootNode) {
        try {
            // 这是一个订阅确认消息
            String subscriptionId = rootNode.get("result").asText();
            String requestId = rootNode.get("id").asText();

            // 使用SolCoinType枚举解析币种和钱包ID
            String coinType = null;
            String walletIdStr = null;

            // 遍历所有支持的币种类型进行匹配
            for (SolCoinType solCoinType : SolCoinType.values()) {
                String coinCode = solCoinType.getCode();
                if (requestId.startsWith(coinCode)) {
                    coinType = coinCode;
                    walletIdStr = requestId.substring(coinCode.length()); // 去掉币种前缀
                    break;
                }
            }

            // 如果无法解析币种，记录警告日志
            if (coinType == null) {
                log.warn("无法解析订阅确认消息中的币种类型，requestId: {}", requestId);
                return;
            }

            log.info("订阅确认: ID={}, 币种={}, 钱包ID={}", subscriptionId, coinType, walletIdStr);

            // 委托给地址订阅器处理确认
            solanaAddressSubscriber.handleSubscriptionConfirmation(subscriptionId, walletIdStr, coinType);
        } catch (Exception e) {
            log.error("处理订阅确认消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleTransactionMessage(JsonNode rootNode) {
        try {
            String signature = rootNode.at("/params/result/value/signature").asText(null);
            if (signature != null && !signature.isEmpty()) {
                log.info("提取到交易签名: {}", signature);
                String subscription = rootNode.at("/params/subscription").asText(null);

                // 通过订阅ID查找关联的地址
                String address = solMonitorManager.getAddressBySubscription(subscription);

                // 发布交易事件
                eventPublisher.publishEvent(new TransactionEvent(this, signature, address));
            }
        } catch (Exception e) {
            log.error("处理交易消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void processTransaction(String signature, String address) {
        // 通过发布TransactionEvent事件来处理交易，不再直接调用
        eventPublisher.publishEvent(new TransactionEvent(this, signature, address));
    }
}
