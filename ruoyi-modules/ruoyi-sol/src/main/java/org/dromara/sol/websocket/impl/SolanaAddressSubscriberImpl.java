package org.dromara.sol.websocket.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.constants.SolRedisKeyConstants;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.dromara.sol.websocket.SolanaAddressSubscriber;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.dromara.sol.enums.SolCoinType.USDC;
import static org.dromara.sol.enums.SolCoinType.USDT;
import static org.dromara.sol.enums.SolCoinType.SOL;

/**
 * Solana地址订阅管理器实现
 * 负责地址订阅和状态管理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaAddressSubscriberImpl implements SolanaAddressSubscriber {

    private final SolMonitorManager solMonitorManager;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final SolanaWebSocketManager solanaWebSocketManager;
    private final RedissonClient redissonClient;

    // 限流器，每秒允许12次请求
    private RRateLimiter rateLimiter;

    // 重订阅状态标志，防止并发重订阅
    private final AtomicBoolean resubscriptionInProgress = new AtomicBoolean(false);

    // 用于Redis操作的线程池
    private final ExecutorService redisOperationExecutor = new ThreadPoolExecutor(
        2, 5, 60, TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(100),
        r -> {
            Thread thread = new Thread(r, "redis-operation");
            thread.setDaemon(true);
            return thread;
        },
        new ThreadPoolExecutor.CallerRunsPolicy()
    );

    /**
     * 初始化限流器
     */
    public void initRateLimiter() {
        // 创建限流器，每秒允许12次请求
        rateLimiter = redissonClient.getRateLimiter("solana:websocket:rate_limiter");
        rateLimiter.trySetRate(RateType.OVERALL, 20, Duration.of(1, ChronoUnit.SECONDS));
    }

    @Override
    public boolean subscribeAddress(String address, Long id, SolCoinType coinType) {
        if (!solanaWebSocketManager.isConnected()) {
            log.warn("WebSocket未连接，无法订阅地址: {}", address);
            return false;
        }

        try {
            // 限流获取令牌
            rateLimiter.acquire(1);

            // 发送WebSocket订阅请求
            String request = getLogSubjectParamStr(address, id, coinType.getCode());
            boolean sent = solanaWebSocketManager.sendMessage(request);

            if (sent) {
                // 更新状态为PENDING
                solMonitorManager.updateAddressStatus(address, SolRedisKeyConstants.STATUS_PENDING);
                log.info("已发送地址{}的订阅请求", address);
            }

            return sent;
        } catch (Exception e) {
            log.error("订阅地址{}失败: {}", address, e.getMessage());
            return false;
        }
    }

    @Override
    public void addMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        try {
            // 添加到Redis监控列表
            solMonitorManager.addMonitorAddress(wallet);

            // 如果WebSocket未连接，则仅添加到Redis，不发送订阅请求
            if (!solanaWebSocketManager.isConnected()) {
                log.warn("WebSocket未连接，仅将地址添加到监控列表，待连接恢复后自动订阅");
                return;
            }

            // 限流发送WebSocket请求
            String solAddress = wallet.getCstAddress();
            String usdtAddress = wallet.getCstUsdtAddress();
            String usdcAddress = wallet.getCstUsdcAddress();
            Long walletId = wallet.getId();

            // 订阅SOL主地址
            if (solAddress != null && !solAddress.isEmpty()) {
                boolean success = subscribeAddress(solAddress, walletId, SOL);
                if (!success) {
                    solMonitorManager.updateAddressStatus(solAddress, SolRedisKeyConstants.STATUS_FAILED);
                }
            }

            // 订阅USDT地址
            if (usdtAddress != null && !usdtAddress.isEmpty()) {
                boolean success = subscribeAddress(usdtAddress, walletId, USDT);
                if (!success) {
                    solMonitorManager.updateAddressStatus(usdtAddress, SolRedisKeyConstants.STATUS_FAILED);
                }
            }

            // 订阅USDC地址
            if (usdcAddress != null && !usdcAddress.isEmpty()) {
                boolean success = subscribeAddress(usdcAddress, walletId, USDC);
                if (!success) {
                    solMonitorManager.updateAddressStatus(usdcAddress, SolRedisKeyConstants.STATUS_FAILED);
                }
            }
        } catch (Exception e) {
            log.error("添加监控地址失败: {}", e.getMessage(), e);
            updateAddressStatusOnFailure(wallet);
        }
    }

    /**
     * 更新地址状态为失败
     * 当添加监控地址失败时调用
     *
     * @param wallet 钱包信息
     */
    private void updateAddressStatusOnFailure(MetaSolanaCstaddressinfoVo wallet) {
        // 更新SOL主地址状态
        String solAddress = wallet.getCstAddress();
        if (solAddress != null && !solAddress.isEmpty()) {
            solMonitorManager.updateAddressStatus(solAddress, SolRedisKeyConstants.STATUS_FAILED);
        }

        // 更新USDT地址状态
        String usdtAddress = wallet.getCstUsdtAddress();
        if (usdtAddress != null && !usdtAddress.isEmpty()) {
            solMonitorManager.updateAddressStatus(usdtAddress, SolRedisKeyConstants.STATUS_FAILED);
        }

        // 更新USDC地址状态
        String usdcAddress = wallet.getCstUsdcAddress();
        if (usdcAddress != null && !usdcAddress.isEmpty()) {
            solMonitorManager.updateAddressStatus(usdcAddress, SolRedisKeyConstants.STATUS_FAILED);
        }
    }

    @Override
    public void resubscribeAllAddresses() {
        // 防止并发重订阅
        if (!resubscriptionInProgress.compareAndSet(false, true)) {
            log.warn("重新订阅正在进行中，跳过本次重订阅请求");
            return;
        }

        try {
            log.info("开始重新订阅所有监控地址...");

            TenantHelper.setDynamic("000000");
            // 直接从数据库获取所有钱包数据，确保数据源一致性
            List<MetaSolanaCstaddressinfoVo> allWallets = solanaCstaddressinfoService.queryAll();
            log.info("从数据库获取到{}个钱包记录，准备订阅相关地址", allWallets.size());

            int totalAddresses = 0;
            int successCount = 0;

            // 遍历每个钱包，订阅其所有地址
            for (MetaSolanaCstaddressinfoVo wallet : allWallets) {
                try {
                    // 订阅USDT地址
                    String usdtAddress = wallet.getCstUsdtAddress();
                    if (usdtAddress != null && !usdtAddress.isEmpty()) {
                        if (subscribeAddress(usdtAddress, wallet.getId(), USDT)) {
                            successCount++;
                        }
                        totalAddresses++;
                    }

                    // 订阅USDC地址
                    String usdcAddress = wallet.getCstUsdcAddress();
                    if (usdcAddress != null && !usdcAddress.isEmpty()) {
                        if (subscribeAddress(usdcAddress, wallet.getId(), USDC)) {
                            successCount++;
                        }
                        totalAddresses++;
                    }

                    // 订阅SOL主地址
                    String solAddress = wallet.getCstAddress();
                    if (solAddress != null && !solAddress.isEmpty()) {
                        if (subscribeAddress(solAddress, wallet.getId(), SOL)) {
                            successCount++;
                        }
                        totalAddresses++;
                    }

                } catch (Exception e) {
                    log.error("重新订阅钱包{}的地址失败: {}", wallet.getId(), e.getMessage());
                    updateAddressStatusOnFailure(wallet);
                }
            }

            log.info("完成所有地址的重新订阅，共处理{}个地址，成功{}个", totalAddresses, successCount);
        } catch (Exception e) {
            log.error("重新订阅地址失败: {}", e.getMessage(), e);
        } finally {
            // 确保重订阅标志被重置
            resubscriptionInProgress.set(false);
        }
    }



    @Override
    public void handleSubscriptionConfirmation(String subscriptionId, String walletIdStr, String coinType) {
        if (walletIdStr == null) {
            return;
        }

        try {
            long walletId = Long.parseLong(walletIdStr);
            MetaSolanaCstaddressinfoVo wallet = solanaCstaddressinfoService.queryById(walletId);

            if (wallet != null) {
                String address = null;

                // 根据币种类型选择相应的地址
                switch (coinType) {
                    case "SOL":
                        address = wallet.getCstAddress();
                        break;
                    case "USDT":
                        address = wallet.getCstUsdtAddress();
                        break;
                    case "USDC":
                        address = wallet.getCstUsdcAddress();
                        break;
                    default:
                        log.warn("未知的币种类型: {}", coinType);
                        return;
                }

                if (address != null && !address.isEmpty()) {
                    // 更新状态为MONITORING
                    solMonitorManager.updateAddressStatus(address, SolRedisKeyConstants.STATUS_MONITORING);

                    // 保存订阅ID与地址的映射关系
                    solMonitorManager.saveSubscriptionMapping(subscriptionId, address);

                    log.info("地址{}订阅成功，已更新状态为MONITORING并保存订阅关系", address);
                }
            }
        } catch (Exception e) {
            log.error("处理订阅确认消息失败: {}", e.getMessage());
        }
    }

    @Override
    public Set<String> getAllMonitorAddresses() {
        return solMonitorManager.getAllMonitorAddresses();
    }

    @Override
    public Set<String> getAddressesByStatus(String status) {
        try {
            // 使用Future在独立线程中执行Redis操作，并设置超时
            Future<Set<String>> future = redisOperationExecutor.submit(() ->
                solMonitorManager.getAddressesByStatus(status)
            );

            // 最多等待5秒
            return future.get(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("获取监控地址操作超时", e);
            return Collections.emptySet();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取监控地址操作被中断", e);
            return Collections.emptySet();
        } catch (ExecutionException e) {
            log.error("获取监控地址操作失败", e.getCause());
            return Collections.emptySet();
        } catch (Exception e) {
            log.error("获取监控地址时发生未预期的错误", e);
            return Collections.emptySet();
        }
    }

    /**
     * 构建日志订阅请求参数
     */
    private static String getLogSubjectParamStr(String address, Long id, String coinType) {
        Map<String, Object> filter = new HashMap<>();
        filter.put("mentions", Collections.singletonList(address));

        Map<String, Object> configObj = new HashMap<>();
        configObj.put("commitment", "finalized");

        Map<String, Object> request = new HashMap<>();
        request.put("jsonrpc", "2.0");
        request.put("id", coinType + id);
        request.put("method", "logsSubscribe");
        request.put("params", Arrays.asList(filter, configObj));

        return JsonUtils.toJsonString(request);
    }
}
