package org.dromara.sol.websocket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * WebSocket配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sol.websocket")
public class WebSocketConfig {
    
    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 30;
    
    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 5;
    
    /**
     * 初始重连延迟（毫秒）
     */
    private long initialReconnectDelay = 1000;
    
    /**
     * 最大重连延迟（毫秒）
     */
    private long maxReconnectDelay = 30000;
    
    /**
     * 重连冷却期（毫秒）
     */
    private long reconnectCooldownPeriod = 300000;
    
    /**
     * 心跳检查间隔（秒）
     */
    private int heartbeatInterval = 60;
    
    /**
     * 是否启用自动重连
     */
    private boolean autoReconnect = true;
    
    /**
     * 是否启用心跳检查
     */
    private boolean heartbeatEnabled = true;
}
