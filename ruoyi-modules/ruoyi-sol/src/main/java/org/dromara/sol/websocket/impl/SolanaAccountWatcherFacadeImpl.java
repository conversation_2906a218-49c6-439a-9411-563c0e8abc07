package org.dromara.sol.websocket.impl;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.event.WalletMonitorEvent;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.dromara.sol.websocket.SolanaAccountWatcherFacade;
import org.dromara.sol.websocket.SolanaAddressSubscriber;
import org.dromara.sol.websocket.SolanaTransactionProcessor;
import org.dromara.sol.websocket.SolanaTransactionScanner;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Solana账户监控器外观实现
 * 整合了WebSocket连接、地址订阅、交易处理和交易扫描等组件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaAccountWatcherFacadeImpl implements SolanaAccountWatcherFacade {

    private final SolanaWebSocketManager solanaWebSocketManager;
    private final SolanaAddressSubscriber solanaAddressSubscriber;
    private final SolanaTransactionProcessor solanaTransactionProcessor;
    private final SolanaTransactionScanner solanaTransactionScanner;
    private final SolMonitorManager solMonitorManager;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    // 连接等待锁，用于等待WebSocket连接建立
    private final AtomicReference<CountDownLatch> connectLatch = new AtomicReference<>(new CountDownLatch(1));

    // 最后断连时间，用于检测长时间断连
    private volatile long lastDisconnectTime = 0;

    // 订阅状态标志，防止重复订阅
    private final AtomicBoolean isInitialSubscriptionCompleted = new AtomicBoolean(false);
    private final AtomicBoolean isSubscriptionInProgress = new AtomicBoolean(false);

    @PostConstruct
    public void init() {
        // 设置WebSocket回调函数
        setupWebSocketCallbacks();

        // 启动交易检查器
        solanaTransactionScanner.startMissedTransactionChecker();
    }

    /**
     * 设置WebSocket回调函数
     */
    private void setupWebSocketCallbacks() {
        // 连接打开回调
        solanaWebSocketManager.setOnOpenCallback(this::handleWebSocketOpen);

        // 连接关闭回调
        solanaWebSocketManager.setOnCloseCallback(this::handleWebSocketClose);

        // 不再设置消息回调，因为现在使用事件机制处理消息
    }

    /**
     * 处理WebSocket连接打开事件
     */
    private void handleWebSocketOpen(ServerHandshake handshake) {
        log.info("WebSocket连接已打开，HTTP状态: {}", handshake.getHttpStatus());

        // 通知等待的线程连接已建立
        CountDownLatch latch = connectLatch.get();
        if (latch != null) {
            latch.countDown();
        }

        // 只有在初始订阅完成后的重连情况下才自动重新订阅
        // 避免系统启动时的重复订阅
        if (isInitialSubscriptionCompleted.get() && !isSubscriptionInProgress.get()) {
            log.info("WebSocket连接已恢复，开始自动重新订阅所有地址");
            solanaAddressSubscriber.resubscribeAllAddresses();
        } else if (!isInitialSubscriptionCompleted.get()) {
            log.info("WebSocket连接已建立，等待主线程完成初始订阅");
        } else {
            log.info("WebSocket连接已建立，但订阅正在进行中，跳过重复订阅");
        }

        // 检查是否是长时间断连后的重连
        long reconnectTime = System.currentTimeMillis();
        solanaTransactionScanner.checkLongDisconnection(reconnectTime, lastDisconnectTime);

        // 触发连接成功时的交易补偿检查
        log.info("WebSocket连接成功，触发遗漏交易补偿检查");
        solanaTransactionScanner.triggerCompensationOnConnection();

        // 重置定时任务调度器，重新开始60分钟周期
        solanaTransactionScanner.resetScheduler();
    }

    /**
     * 处理WebSocket连接关闭事件
     */
    private void handleWebSocketClose(SolanaWebSocketManager.CloseInfo closeInfo) {
        log.info("连接关闭 [代码: {}, 原因: {}, 是否远程关闭: {}]",
                closeInfo.getCode(), closeInfo.getReason(), closeInfo.isRemote());

        // 记录断连时间，用于检测长时间断连
        lastDisconnectTime = System.currentTimeMillis();

        // 重置连接等待信号量，为下一次连接做准备
        connectLatch.set(new CountDownLatch(1));
    }

    private static void setTenant() {
        TenantHelper.setDynamic("000000", true);
    }

    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void cleanup() {
        try {
            // 完全清理WebSocket资源（关闭连接和线程池）
            solanaWebSocketManager.cleanup();

            // 关闭交易扫描器
            solanaTransactionScanner.shutdown();

            log.info("SolanaAccountWatcher清理完成");
        } catch (Exception e) {
            log.error("SolanaAccountWatcher清理过程中发生错误", e);
        }
    }

    @Override
    public void run(ApplicationArguments args) {
        setTenant();

        // 初始化地址订阅器的限流器
        if (solanaAddressSubscriber instanceof SolanaAddressSubscriberImpl) {
            ((SolanaAddressSubscriberImpl)solanaAddressSubscriber).initRateLimiter();
        }

        // 系统启动时清空Redis并从数据库重建所有监控数据
        try {
            int rebuiltCount = solMonitorManager.resetAllMonitoringStatus();
            log.info("系统启动，已从数据库重建{}个地址的监控数据", rebuiltCount);
        } catch (Exception e) {
            log.error("重建监控数据失败", e);
        }

        // 初始化WebSocket连接
        initializeWebSocketClient();

        try {
            // 等待WebSocket连接建立，最多等待30秒
            boolean connected = solanaWebSocketManager.waitForConnection(30, TimeUnit.SECONDS);
            if (!connected) {
                log.error("WebSocket连接超时，将尝试自动重连");
                return;
            }

            log.info("WebSocket连接已建立，开始订阅所有监控地址");

            // 设置订阅进行中标志，防止重复订阅
            isSubscriptionInProgress.set(true);

            try {
                // 获取所有监控地址进行订阅（数据已经在Redis中了）
                List<MetaSolanaCstaddressinfoVo> wallets = solanaCstaddressinfoService.queryAll();
                for (MetaSolanaCstaddressinfoVo wallet : wallets) {
                    // 直接订阅地址（Redis数据已经存在）
                    this.addSubjectMonitorAddress(wallet);
                }

                // 标记初始订阅完成
                isInitialSubscriptionCompleted.set(true);
                log.info("初始订阅完成，共处理{}个钱包", wallets.size());

                // 等待所有地址订阅成功（给予一些时间让订阅完成）
                log.info("等待地址订阅完成后进行全量扫描...");
                TimeUnit.SECONDS.sleep(15);

                // 系统启动后触发一次全量扫描
                scanAllAddressesForMissedTransactions();

            } finally {
                // 清除订阅进行中标志
                isSubscriptionInProgress.set(false);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("初始化监控地址时被中断", e);
        } catch (Exception e) {
            log.error("初始化监控地址失败", e);
        }
    }

    @Override
    public boolean isConnected() {
        return solanaWebSocketManager.isConnected();
    }

    @Override
    public boolean subscribeAddress(String address, Long id, SolCoinType coinType) {
        return solanaAddressSubscriber.subscribeAddress(address, id, coinType);
    }

    @Override
    public void addSubjectMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        solanaAddressSubscriber.addMonitorAddress(wallet);
    }

    @Override
    public void resubscribeAllAddresses() {
        // 设置订阅进行中标志，防止重复订阅
        if (isSubscriptionInProgress.compareAndSet(false, true)) {
            try {
                log.info("开始重新订阅所有地址...");
                solanaAddressSubscriber.resubscribeAllAddresses();
                log.info("重新订阅完成");
            } finally {
                isSubscriptionInProgress.set(false);
            }
        } else {
            log.info("订阅正在进行中，跳过重复的重新订阅请求");
        }
    }

    @Override
    public void scanAllAddressesForMissedTransactions() {
        solanaTransactionScanner.scanAllAddressesForMissedTransactions();
    }

    @Override
    public void initializeWebSocketClient() {
        solanaWebSocketManager.initializeWebSocketClient();
    }

    /**
     * 监听钱包监控事件
     *
     * @param event 钱包监控事件
     */
    @EventListener
    public void handleWalletMonitorEvent(WalletMonitorEvent event) {
        MetaSolanaCstaddressinfoVo wallet = event.getWallet();
        addSubjectMonitorAddress(wallet);
    }
}
