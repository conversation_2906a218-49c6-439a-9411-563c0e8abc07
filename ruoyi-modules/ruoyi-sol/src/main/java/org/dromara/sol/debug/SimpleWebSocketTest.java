package org.dromara.sol.debug;

import lombok.extern.slf4j.Slf4j;

import jakarta.websocket.*;
import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 简单的WebSocket测试 - 使用Java标准WebSocket API
 */
@Slf4j
@ClientEndpoint
public class SimpleWebSocketTest {

    private static final String QUICKNODE_WSS_URL = "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/";
    private CountDownLatch connectionLatch = new CountDownLatch(1);
    private Session session;

    public static void main(String[] args) {
        SimpleWebSocketTest test = new SimpleWebSocketTest();
        test.testConnection();
    }

    public void testConnection() {
        try {
            log.info("=== 使用Java标准WebSocket API测试连接 ===");
            log.info("连接到: {}", QUICKNODE_WSS_URL);

            WebSocketContainer container = ContainerProvider.getWebSocketContainer();
            
            // 设置连接超时
            container.setDefaultMaxSessionIdleTimeout(30000);
            
            URI uri = new URI(QUICKNODE_WSS_URL);
            session = container.connectToServer(this, uri);

            // 等待连接建立
            boolean connected = connectionLatch.await(30, TimeUnit.SECONDS);

            if (connected) {
                log.info("✅ 连接成功建立！");
                
                // 发送测试消息
                sendTestMessage();
                
                // 等待响应
                Thread.sleep(5000);
                
            } else {
                log.error("❌ 连接超时");
            }

        } catch (Exception e) {
            log.error("连接失败: {}", e.getMessage(), e);
        } finally {
            cleanup();
        }
    }

    @OnOpen
    public void onOpen(Session session) {
        log.info("✅ WebSocket连接已打开");
        log.info("会话ID: {}", session.getId());
        log.info("协议版本: {}", session.getProtocolVersion());
        log.info("最大消息大小: {}", session.getMaxTextMessageBufferSize());
        this.session = session;
        connectionLatch.countDown();
    }

    @OnMessage
    public void onMessage(String message) {
        log.info("📨 收到消息: {}", message.length() > 200 ? 
            message.substring(0, 200) + "..." : message);
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.info("🔌 连接关闭: [{}] {}", closeReason.getCloseCode(), closeReason.getReasonPhrase());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("❌ WebSocket错误: {}", throwable.getMessage(), throwable);
        connectionLatch.countDown();
    }

    private void sendTestMessage() {
        try {
            if (session != null && session.isOpen()) {
                String testMessage = """
                    {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "getHealth"
                    }
                    """;
                
                log.info("📤 发送测试消息: {}", testMessage.trim());
                session.getBasicRemote().sendText(testMessage);
            }
        } catch (Exception e) {
            log.error("发送消息失败", e);
        }
    }

    private void cleanup() {
        try {
            if (session != null && session.isOpen()) {
                session.close();
                log.info("🧹 连接已清理");
            }
        } catch (Exception e) {
            log.warn("清理连接时发生错误", e);
        }
    }
}
