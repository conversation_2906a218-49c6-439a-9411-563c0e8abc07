package org.dromara.sol.websocket.impl;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.event.WebSocketMessageEvent;
import org.dromara.sol.exception.SolanaURIFormatException;
import org.dromara.sol.exception.SolanaWebSocketConnectionException;
import org.dromara.sol.websocket.SolanaWebSocketManager;
import org.dromara.sol.websocket.impl.SpringWebSocketClientAdapter;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * Solana WebSocket连接管理器实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SolanaWebSocketManagerImpl implements SolanaWebSocketManager {

    private final SolRpcConfig rpcConfig;
    private final ApplicationEventPublisher eventPublisher;

    private SpringWebSocketClientAdapter client;

    // 连接状态标志
    private final AtomicBoolean isConnected = new AtomicBoolean(false);

    // 重连相关参数
    private static final int MAX_RECONNECT_ATTEMPTS = 10; // 最大重连次数
    private static final long INITIAL_RECONNECT_DELAY_MS = 1000; // 初始重连延迟（毫秒）
    private static final long MAX_RECONNECT_DELAY_MS = 60000; // 最大重连延迟（1分钟）
    private static final long RECONNECT_COOLDOWN_PERIOD_MS = 300000; // 重连冷却期（5分钟）
    private final AtomicInteger reconnectAttempts = new AtomicInteger(0); // 当前重连尝试次数
    private volatile long lastReconnectResetTime = 0; // 上次重置重连尝试计数的时间

    // 重连任务执行器
    private final ScheduledExecutorService reconnectExecutor = new ScheduledThreadPoolExecutor(1, r -> {
        Thread thread = new Thread(r, "solana-websocket-reconnect");
        thread.setDaemon(true); // 设置为守护线程
        return thread;
    });

    // 连接守护线程执行器
    private final ScheduledExecutorService connectionWatchdog = new ScheduledThreadPoolExecutor(1, r -> {
        Thread thread = new Thread(r, "solana-websocket-watchdog");
        thread.setDaemon(true); // 设置为守护线程
        return thread;
    });

    // 连接等待锁，用于等待WebSocket连接建立
    private final AtomicReference<CountDownLatch> connectLatch = new AtomicReference<>(new CountDownLatch(1));


    // 回调函数
    private Consumer<WebSocketSession> onOpenCallback;
    private Consumer<String> onMessageCallback;
    private Consumer<CloseStatus> onCloseCallback;

    /**
     * 初始化方法，启动连接监控
     */
    @PostConstruct
    public void init() {
        log.info("初始化SolanaWebSocketManager，启动连接监控守护线程");
        startConnectionWatchdog();
    }

    /**
     * 启动连接监控守护线程
     */
    public void startConnectionWatchdog() {
        connectionWatchdog.scheduleAtFixedRate(() -> {
            try {
                log.debug("连接守护线程检查WebSocket状态...");
                if (!isConnected() && reconnectAttempts.get() == 0) {
                    // 检查是否在冷却期内
                    long now = System.currentTimeMillis();
                    if (lastReconnectResetTime > 0 && now - lastReconnectResetTime < RECONNECT_COOLDOWN_PERIOD_MS) {
                        log.debug("仍在冷却期内，守护线程暂不触发重连");
                        return;
                    }

                    // 如果连接断开且没有正在进行的重连尝试
                    log.warn("连接守护线程检测到WebSocket断开且没有重连尝试，将强制启动重连");
                    reconnectAttempts.set(0); // 重置尝试次数
                    scheduleReconnect();
                }
            } catch (Exception e) {
                log.error("连接守护线程执行异常", e);
            }
        }, 60, 60, TimeUnit.SECONDS);
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void cleanup() {
        try {
            if (client != null) {
                client.close();
            }

            reconnectExecutor.shutdown();
            connectionWatchdog.shutdown();

            log.info("SolanaWebSocketManager清理完成");
        } catch (Exception e) {
            log.error("SolanaWebSocketManager清理过程中发生错误", e);
        }
    }

    @Override
    public void initializeWebSocketClient() {
        try {
            // 检查WebSocket URL是否配置
            String solanaWebsocketUrl = rpcConfig.getWebsocketUrl();
            if (solanaWebsocketUrl == null || solanaWebsocketUrl.trim().isEmpty()) {
                log.error("WebSocket URL未配置，无法建立连接");
                return;
            }

            log.info("初始化WebSocket客户端，URL: {}", solanaWebsocketUrl);

            // 创建WebSocket客户端
            client = new SpringWebSocketClientAdapter();

            // 设置回调函数
            setupClientCallbacks();

            // 启动连接
            URI websocketUri = new URI(solanaWebsocketUrl);
            client.connect(websocketUri);

            log.info("正在连接WebSocket...");

            // 等待连接建立 (最多30秒)
            boolean connected = client.waitForConnection(30, TimeUnit.SECONDS);

            if (connected) {
                log.info("WebSocket连接成功建立");
            } else {
                log.error("WebSocket连接超时 - 30秒内未能建立连接");
                // 可能的原因分析
                log.error("可能的原因：");
                log.error("1. 网络连接问题");
                log.error("2. WebSocket URL错误或路径不正确");
                log.error("3. 需要API密钥认证 (当前URL: {})", solanaWebsocketUrl);
                log.error("4. 服务器SSL/TLS配置问题");
                log.error("建议：检查QuickNode仪表板获取正确的WebSocket端点和认证信息");
            }

        } catch (SolanaURIFormatException e) {
            log.error("WebSocket URI格式错误: {}", e.getMessage(), e);
        } catch (SolanaWebSocketConnectionException e) {
            log.error("WebSocket连接初始化失败: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("初始化WebSocket失败: {}", e.getMessage(), e);
            if (e.getCause() != null) {
                log.error("根本原因: {}", e.getCause().getMessage());
            }
        }
    }

    @Override
    public boolean isConnected() {
        return isConnected.get() && client != null && client.isConnected();
    }

    @Override
    public boolean sendMessage(String message) {
        if (!isConnected()) {
            log.warn("WebSocket未连接，无法发送消息");
            return false;
        }

        return client.sendMessage(message);
    }

    @Override
    public void closeConnection() {
        if (client != null) {
            client.close();
        }
    }

    @Override
    public void setOnOpenCallback(Consumer<WebSocketSession> onOpenCallback) {
        this.onOpenCallback = onOpenCallback;
    }

    @Override
    public void setOnMessageCallback(Consumer<String> onMessageCallback) {
        this.onMessageCallback = onMessageCallback;
    }

    @Override
    public void setOnCloseCallback(Consumer<CloseStatus> onCloseCallback) {
        this.onCloseCallback = onCloseCallback;
    }

    /**
     * 设置客户端回调函数
     */
    private void setupClientCallbacks() {
        client.setOnOpenCallback(this::handleWebSocketOpen);
        client.setOnMessageCallback(this::handleWebSocketMessage);
        client.setOnCloseCallback(this::handleWebSocketClose);
        client.setOnErrorCallback(ex -> log.error("WebSocket发生错误", ex));
    }

    /**
     * 处理WebSocket连接打开事件
     */
    private void handleWebSocketOpen(WebSocketSession session) {
        log.info("WebSocket连接已打开");

        // 更新连接状态
        isConnected.set(true);

        // 重置重连尝试次数
        reconnectAttempts.set(0);

        // 通知等待的线程连接已建立
        CountDownLatch latch = connectLatch.get();
        if (latch != null) {
            latch.countDown();
        }

        // 调用回调函数
        if (onOpenCallback != null) {
            onOpenCallback.accept(session);
        }
    }

    /**
     * 处理WebSocket消息
     */
    private void handleWebSocketMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        // 发布WebSocket消息事件
        eventPublisher.publishEvent(new WebSocketMessageEvent(this, message));

        // 调用回调函数
        if (onMessageCallback != null) {
            onMessageCallback.accept(message);
        }
    }

    /**
     * 处理WebSocket连接关闭事件
     */
    private void handleWebSocketClose(CloseStatus closeStatus) {
        log.info("连接关闭 [代码: {}, 原因: {}]", closeStatus.getCode(), closeStatus.getReason());

        // 更新连接状态
        isConnected.set(false);

        // 重置连接等待信号量，为下一次连接做准备
        connectLatch.set(new CountDownLatch(1));

        // 调用回调函数
        if (onCloseCallback != null) {
            onCloseCallback.accept(closeStatus);
        }

        // 安排重连
        scheduleReconnect();
    }

    @Override
    public boolean waitForConnection(long timeout, TimeUnit unit) throws InterruptedException {
        if (client != null) {
            return client.waitForConnection(timeout, unit);
        }
        return false;
    }

    /**
     * 使用指数退避算法安排重连
     */
    private void scheduleReconnect() {
        int attempts = reconnectAttempts.incrementAndGet();

        // 检查是否达到最大重连次数
        if (attempts > MAX_RECONNECT_ATTEMPTS) {
            handleMaxReconnectAttemptsReached();
            return;
        }

        // 检查冷却期
        if (isInCooldownPeriod()) {
            return;
        }

        // 计算重连延迟并安排任务
        scheduleReconnectAttempt(attempts);
    }

    /**
     * 处理达到最大重连次数的情况
     */
    private void handleMaxReconnectAttemptsReached() {
        log.error("达到最大重连次数({}次)，进入冷却期", MAX_RECONNECT_ATTEMPTS);

        // 记录当前时间，用于判断冷却期是否结束
        lastReconnectResetTime = System.currentTimeMillis();

        // 重置尝试次数
        reconnectAttempts.set(0);

        // 安排一个延迟任务，在冷却期结束后尝试重连
        reconnectExecutor.schedule(() -> {
            log.info("冷却期结束，重新尝试连接");
            initializeWebSocketClient();
        }, RECONNECT_COOLDOWN_PERIOD_MS, TimeUnit.MILLISECONDS);
    }

    /**
     * 检查是否处于冷却期
     *
     * @return 是否在冷却期内
     */
    private boolean isInCooldownPeriod() {
        long now = System.currentTimeMillis();
        if (lastReconnectResetTime > 0 && now - lastReconnectResetTime < RECONNECT_COOLDOWN_PERIOD_MS) {
            log.info("仍在冷却期内，跳过本次重连");
            return true;
        }
        return false;
    }

    /**
     * 计算重连延迟并安排重连任务
     *
     * @param attempts 当前尝试次数
     */
    private void scheduleReconnectAttempt(int attempts) {
        // 计算下次重连延迟（指数退避算法）
        long delay = calculateReconnectDelay(attempts);
        log.info("计划在{}ms后进行第{}次重连尝试", delay, attempts);

        // 安排重连任务
        reconnectExecutor.schedule(() -> executeReconnect(attempts), delay, TimeUnit.MILLISECONDS);
    }

    /**
     * 计算重连延迟时间
     *
     * @param attempts 当前尝试次数
     * @return 延迟时间（毫秒）
     */
    private long calculateReconnectDelay(int attempts) {
        return Math.min(
            INITIAL_RECONNECT_DELAY_MS * (1L << (attempts - 1)), // 1s, 2s, 4s, 8s, ...
            MAX_RECONNECT_DELAY_MS // 最大不超过60s
        );
    }

    /**
     * 执行重连尝试
     *
     * @param attempts 当前尝试次数
     */
    private void executeReconnect(int attempts) {
        try {
            log.info("执行第{}次重连...", attempts);

            // 如果连接已经恢复，则不需要重连
            if (isConnected()) {
                log.info("连接已经恢复，取消重连");
                reconnectAttempts.set(0);
                return;
            }

            // 根据客户端状态执行不同的重连策略
            reconnectBasedOnClientState(attempts);

        } catch (Exception e) {
            log.error("第{}次重连失败: {}", attempts, e.getMessage());
            // 如果重连失败，继续安排下一次重连
            scheduleReconnect();
        }
    }

    /**
     * 根据客户端状态执行不同的重连策略
     *
     * @param attempts 当前尝试次数
     */
    private void reconnectBasedOnClientState(int attempts) {
        if (client == null) {
            // 如果客户端为null，重新初始化
            initializeWebSocketClient();
            return;
        }

        if (client.isConnected()) {
            // 如果客户端仍处于连接状态（但isConnected为false），关闭后再重连
            reconnectWithOpenClient(attempts);
        } else {
            // 客户端已关闭，直接尝试重连
            reconnectWithClosedClient();
        }
    }

    /**
     * 处理客户端仍处于打开状态的重连
     *
     * @param attempts 当前尝试次数
     */
    private void reconnectWithOpenClient(int attempts) {
        try {
            client.close();
            // 在重连之前稍作延迟
            scheduleDelayedReconnect(attempts, 1000);
        } catch (Exception e) {
            log.warn("关闭现有连接时发生错误", e);
            // 继续尝试重连
            scheduleReconnect();
        }
    }

    /**
     * 安排延迟重连
     *
     * @param attempts 当前尝试次数
     * @param delayMs  延迟毫秒数
     */
    private void scheduleDelayedReconnect(int attempts, long delayMs) {
        reconnectExecutor.schedule(() -> attemptReconnectForOpenClient(attempts), delayMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 尝试为打开状态的客户端重连
     *
     * @param attempts 当前尝试次数
     */
    private void attemptReconnectForOpenClient(int attempts) {
        try {
            if (client != null) {
                tryClientReconnect();
            } else {
                // 如果客户端为null，重新初始化
                initializeWebSocketClient();
            }
        } catch (Exception e) {
            log.error("第{}次重连失败: {}", attempts, e.getMessage());
            // 如果重连失败，继续安排下一次重连
            scheduleReconnect();
        }
    }

    /**
     * 尝试客户端重连
     */
    private void tryClientReconnect() {
        try {
            // 关闭现有连接并重新初始化
            client.close();
            initializeWebSocketClient();
        } catch (Exception e) {
            log.error("重连失败，将重新初始化WebSocket", e);
            // 如果关闭失败，强制重新初始化客户端
            initializeWebSocketClient();
        }
    }

    /**
     * 处理客户端已关闭状态的重连
     */
    private void reconnectWithClosedClient() {
        try {
            // 直接重新初始化客户端
            initializeWebSocketClient();
        } catch (Exception e) {
            log.error("重连失败，将重新初始化WebSocket", e);
            // 如果初始化失败，再次尝试初始化客户端
            initializeWebSocketClient();
        }
    }


}
