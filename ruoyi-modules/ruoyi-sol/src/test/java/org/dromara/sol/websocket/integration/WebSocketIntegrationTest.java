package org.dromara.sol.websocket.integration;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.core.WebSocketConnection;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket集成测试
 * 这些测试需要真实的网络连接，只在特定环境下运行
 */
@Slf4j
class WebSocketIntegrationTest {

    private static final String ECHO_WEBSOCKET_URL = "ws://echo.websocket.org/";
    private static final String SOLANA_DEVNET_URL = "wss://api.devnet.solana.com/";

    @Test
    @EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
    void testEchoWebSocketConnection() throws Exception {
        WebSocketConfig config = createTestConfig();
        WebSocketConnection connection = new WebSocketConnection(new URI(ECHO_WEBSOCKET_URL), config);

        CountDownLatch openLatch = new CountDownLatch(1);
        CountDownLatch messageLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicReference<String> receivedMessage = new AtomicReference<>();

        // 设置回调
        connection.setOnOpenCallback(handshake -> {
            log.info("Echo WebSocket连接成功");
            connected.set(true);
            openLatch.countDown();
        });

        connection.setOnMessageCallback(message -> {
            log.info("收到Echo消息: {}", message);
            receivedMessage.set(message);
            messageLatch.countDown();
        });

        connection.setOnErrorCallback(error -> {
            log.error("Echo WebSocket错误", error);
            openLatch.countDown();
            messageLatch.countDown();
        });

        try {
            // 连接
            boolean connectResult = connection.connect();
            assertTrue(connectResult, "应该能够连接到Echo WebSocket");

            // 等待连接建立
            assertTrue(openLatch.await(10, TimeUnit.SECONDS), "连接应该在10秒内建立");
            assertTrue(connected.get(), "连接状态应该为true");
            assertTrue(connection.isConnected(), "isConnected()应该返回true");

            // 发送测试消息
            String testMessage = "Hello WebSocket!";
            boolean sendResult = connection.sendMessage(testMessage);
            assertTrue(sendResult, "消息发送应该成功");

            // 等待回显消息
            assertTrue(messageLatch.await(5, TimeUnit.SECONDS), "应该在5秒内收到回显消息");
            assertEquals(testMessage, receivedMessage.get(), "收到的消息应该与发送的消息相同");

        } finally {
            connection.close();
        }
    }

    @Test
    @EnabledIfEnvironmentVariable(named = "RUN_INTEGRATION_TESTS", matches = "true")
    void testSolanaDevnetConnection() throws Exception {
        WebSocketConfig config = createTestConfig();
        WebSocketConnection connection = new WebSocketConnection(new URI(SOLANA_DEVNET_URL), config);

        CountDownLatch openLatch = new CountDownLatch(1);
        CountDownLatch messageLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicReference<String> receivedMessage = new AtomicReference<>();

        // 设置回调
        connection.setOnOpenCallback(handshake -> {
            log.info("Solana Devnet WebSocket连接成功");
            connected.set(true);
            openLatch.countDown();
        });

        connection.setOnMessageCallback(message -> {
            log.info("收到Solana消息: {}", message);
            receivedMessage.set(message);
            messageLatch.countDown();
        });

        connection.setOnErrorCallback(error -> {
            log.error("Solana WebSocket错误", error);
            openLatch.countDown();
        });

        try {
            // 连接
            boolean connectResult = connection.connect();
            assertTrue(connectResult, "应该能够连接到Solana Devnet WebSocket");

            // 等待连接建立
            assertTrue(openLatch.await(15, TimeUnit.SECONDS), "连接应该在15秒内建立");
            assertTrue(connected.get(), "连接状态应该为true");
            assertTrue(connection.isConnected(), "isConnected()应该返回true");

            // 发送Solana RPC请求
            String rpcRequest = """
                {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                """;

            boolean sendResult = connection.sendMessage(rpcRequest);
            assertTrue(sendResult, "RPC请求发送应该成功");

            // 等待响应
            assertTrue(messageLatch.await(10, TimeUnit.SECONDS), "应该在10秒内收到RPC响应");
            assertNotNull(receivedMessage.get(), "应该收到响应消息");
            assertTrue(receivedMessage.get().contains("jsonrpc"), "响应应该是有效的JSON-RPC格式");

        } finally {
            connection.close();
        }
    }

    @Test
    void testConnectionFailureHandling() throws Exception {
        WebSocketConfig config = createTestConfig();
        // 使用无效的URL
        WebSocketConnection connection = new WebSocketConnection(new URI("ws://invalid-host-12345.com/"), config);

        CountDownLatch errorLatch = new CountDownLatch(1);
        AtomicReference<Exception> caughtError = new AtomicReference<>();

        connection.setOnErrorCallback(error -> {
            caughtError.set(error);
            errorLatch.countDown();
        });

        // 尝试连接
        boolean connectResult = connection.connect();
        assertFalse(connectResult, "连接到无效主机应该失败");

        // 验证错误处理
        assertTrue(errorLatch.await(10, TimeUnit.SECONDS), "应该在10秒内收到错误");
        assertNotNull(caughtError.get(), "应该捕获到错误");

        assertFalse(connection.isConnected(), "连接状态应该为false");
    }

    @Test
    void testReconnectFunctionality() throws Exception {
        WebSocketConfig config = createTestConfig();
        WebSocketConnection connection = new WebSocketConnection(new URI(ECHO_WEBSOCKET_URL), config);

        CountDownLatch firstConnectLatch = new CountDownLatch(1);
        CountDownLatch reconnectLatch = new CountDownLatch(1);
        AtomicBoolean firstConnected = new AtomicBoolean(false);
        AtomicBoolean reconnected = new AtomicBoolean(false);

        connection.setOnOpenCallback(handshake -> {
            if (!firstConnected.get()) {
                firstConnected.set(true);
                firstConnectLatch.countDown();
            } else {
                reconnected.set(true);
                reconnectLatch.countDown();
            }
        });

        try {
            // 首次连接
            boolean connectResult = connection.connect();
            if (connectResult) {
                assertTrue(firstConnectLatch.await(10, TimeUnit.SECONDS), "首次连接应该成功");

                // 关闭连接
                connection.close();
                assertFalse(connection.isConnected(), "关闭后连接状态应该为false");

                // 重连
                boolean reconnectResult = connection.reconnect();
                if (reconnectResult) {
                    assertTrue(reconnectLatch.await(10, TimeUnit.SECONDS), "重连应该成功");
                    assertTrue(reconnected.get(), "重连状态应该为true");
                }
            }
        } finally {
            connection.close();
        }
    }

    @Test
    void testConcurrentConnections() throws Exception {
        WebSocketConfig config = createTestConfig();
        int connectionCount = 3;
        CountDownLatch allConnectedLatch = new CountDownLatch(connectionCount);
        AtomicBoolean hasError = new AtomicBoolean(false);

        for (int i = 0; i < connectionCount; i++) {
            final int connectionId = i;
            new Thread(() -> {
                try {
                    WebSocketConnection connection = new WebSocketConnection(new URI(ECHO_WEBSOCKET_URL), config);
                    
                    connection.setOnOpenCallback(handshake -> {
                        log.info("连接 {} 建立成功", connectionId);
                        allConnectedLatch.countDown();
                    });

                    connection.setOnErrorCallback(error -> {
                        log.error("连接 {} 发生错误", connectionId, error);
                        hasError.set(true);
                        allConnectedLatch.countDown();
                    });

                    boolean connected = connection.connect();
                    if (connected) {
                        Thread.sleep(1000); // 保持连接1秒
                    }
                    connection.close();

                } catch (Exception e) {
                    log.error("连接 {} 异常", connectionId, e);
                    hasError.set(true);
                    allConnectedLatch.countDown();
                }
            }).start();
        }

        assertTrue(allConnectedLatch.await(30, TimeUnit.SECONDS), "所有连接应该在30秒内完成");
        assertFalse(hasError.get(), "不应该有连接错误");
    }

    private WebSocketConfig createTestConfig() {
        WebSocketConfig config = new WebSocketConfig();
        config.setConnectionTimeout(10);
        config.setMaxReconnectAttempts(3);
        config.setInitialReconnectDelay(1000);
        config.setMaxReconnectDelay(5000);
        config.setAutoReconnect(true);
        config.setHeartbeatEnabled(false);
        return config;
    }
}
