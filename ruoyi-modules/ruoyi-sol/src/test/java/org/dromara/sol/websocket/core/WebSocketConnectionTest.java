package org.dromara.sol.websocket.core;

import org.dromara.sol.websocket.config.WebSocketConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WebSocket连接测试
 */
@ExtendWith(MockitoExtension.class)
class WebSocketConnectionTest {

    @Mock
    private WebSocketConfig config;

    private WebSocketConnection connection;
    private URI testUri;

    @BeforeEach
    void setUp() throws Exception {
        // 设置默认配置
        when(config.getConnectionTimeout()).thenReturn(5);
        when(config.getHeartbeatInterval()).thenReturn(30);
        when(config.isAutoReconnect()).thenReturn(true);
        when(config.isHeartbeatEnabled()).thenReturn(true);

        testUri = new URI("ws://echo.websocket.org/");
        connection = new WebSocketConnection(testUri, config);
    }

    @Test
    void testConnectionInitialization() {
        assertNotNull(connection);
        assertFalse(connection.isConnected());
    }

    @Test
    void testSetCallbacks() {
        AtomicBoolean openCalled = new AtomicBoolean(false);
        AtomicBoolean messageCalled = new AtomicBoolean(false);
        AtomicBoolean closeCalled = new AtomicBoolean(false);
        AtomicBoolean errorCalled = new AtomicBoolean(false);

        connection.setOnOpenCallback(handshake -> openCalled.set(true));
        connection.setOnMessageCallback(message -> messageCalled.set(true));
        connection.setOnCloseCallback(closeInfo -> closeCalled.set(true));
        connection.setOnErrorCallback(error -> errorCalled.set(true));

        // 回调设置不应该抛出异常
        assertDoesNotThrow(() -> {
            connection.setOnOpenCallback(null);
            connection.setOnMessageCallback(null);
            connection.setOnCloseCallback(null);
            connection.setOnErrorCallback(null);
        });
    }

    @Test
    void testSendMessageWhenNotConnected() {
        boolean result = connection.sendMessage("test message");
        assertFalse(result);
    }

    @Test
    void testCloseWhenNotConnected() {
        // 应该不抛出异常
        assertDoesNotThrow(() -> connection.close());
    }

    @Test
    void testReconnectWhenNotConnected() {
        // 模拟重连（实际上是首次连接）
        boolean result = connection.reconnect();
        // 由于使用的是真实的WebSocket URL，可能会失败，但不应该抛出异常
        assertDoesNotThrow(() -> connection.reconnect());
    }

    @Test
    void testAsyncConnect() {
        CompletableFuture<Boolean> future = connection.connectAsync();
        assertNotNull(future);
        
        // 异步连接应该立即返回Future
        assertFalse(future.isDone() && future.join());
    }

    @Test
    void testConnectionCloseInfo() {
        WebSocketConnection.ConnectionCloseInfo closeInfo = 
            new WebSocketConnection.ConnectionCloseInfo(1000, "Normal closure", false);
        
        assertEquals(1000, closeInfo.getCode());
        assertEquals("Normal closure", closeInfo.getReason());
        assertFalse(closeInfo.isRemote());
    }

    @Test
    void testMultipleCloseCallsSafe() {
        // 多次调用close应该是安全的
        assertDoesNotThrow(() -> {
            connection.close();
            connection.close();
            connection.close();
        });
    }

    @Test
    void testCallbackExceptionHandling() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<Exception> caughtException = new AtomicReference<>();

        // 设置一个会抛出异常的回调
        connection.setOnErrorCallback(error -> {
            try {
                throw new RuntimeException("测试异常");
            } catch (Exception e) {
                caughtException.set(e);
                latch.countDown();
            }
        });

        // 触发错误回调（通过尝试连接到无效URI）
        try {
            connection = new WebSocketConnection(new URI("ws://invalid-host-that-does-not-exist.com/"), config);
            connection.connect();
        } catch (Exception e) {
            // 预期的异常
        }

        // 验证异常处理不会影响连接的稳定性
        assertDoesNotThrow(() -> connection.close());
    }

    @Test
    void testConcurrentOperations() throws InterruptedException {
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicBoolean hasException = new AtomicBoolean(false);

        // 并发执行多个操作
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    connection.sendMessage("test");
                    connection.isConnected();
                    connection.close();
                } catch (Exception e) {
                    hasException.set(true);
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        assertTrue(latch.await(5, TimeUnit.SECONDS));
        assertFalse(hasException.get(), "并发操作不应该产生异常");
    }

    @Test
    void testConnectionStateConsistency() {
        // 初始状态
        assertFalse(connection.isConnected());

        // 尝试连接后状态（可能失败，但状态应该一致）
        connection.connectAsync();
        
        // 关闭后状态
        connection.close();
        assertFalse(connection.isConnected());
    }
}
