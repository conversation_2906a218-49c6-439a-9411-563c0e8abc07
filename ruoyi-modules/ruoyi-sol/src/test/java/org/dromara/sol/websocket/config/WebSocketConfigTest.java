package org.dromara.sol.websocket.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket配置测试
 */
@SpringBootTest(classes = WebSocketConfig.class)
@TestPropertySource(properties = {
    "sol.websocket.connection-timeout=45",
    "sol.websocket.max-reconnect-attempts=3",
    "sol.websocket.initial-reconnect-delay=2000",
    "sol.websocket.max-reconnect-delay=60000",
    "sol.websocket.reconnect-cooldown-period=600000",
    "sol.websocket.heartbeat-interval=30",
    "sol.websocket.auto-reconnect=false",
    "sol.websocket.heartbeat-enabled=false"
})
class WebSocketConfigTest {

    @Test
    void testDefaultValues() {
        WebSocketConfig config = new WebSocketConfig();
        
        assertEquals(30, config.getConnectionTimeout());
        assertEquals(5, config.getMaxReconnectAttempts());
        assertEquals(1000, config.getInitialReconnectDelay());
        assertEquals(30000, config.getMaxReconnectDelay());
        assertEquals(300000, config.getReconnectCooldownPeriod());
        assertEquals(60, config.getHeartbeatInterval());
        assertTrue(config.isAutoReconnect());
        assertTrue(config.isHeartbeatEnabled());
    }

    @Test
    void testConfigurationProperties() {
        WebSocketConfig config = new WebSocketConfig();
        
        // 测试设置值
        config.setConnectionTimeout(45);
        config.setMaxReconnectAttempts(3);
        config.setInitialReconnectDelay(2000);
        config.setMaxReconnectDelay(60000);
        config.setReconnectCooldownPeriod(600000);
        config.setHeartbeatInterval(30);
        config.setAutoReconnect(false);
        config.setHeartbeatEnabled(false);
        
        // 验证设置的值
        assertEquals(45, config.getConnectionTimeout());
        assertEquals(3, config.getMaxReconnectAttempts());
        assertEquals(2000, config.getInitialReconnectDelay());
        assertEquals(60000, config.getMaxReconnectDelay());
        assertEquals(600000, config.getReconnectCooldownPeriod());
        assertEquals(30, config.getHeartbeatInterval());
        assertFalse(config.isAutoReconnect());
        assertFalse(config.isHeartbeatEnabled());
    }

    @Test
    void testValidationConstraints() {
        WebSocketConfig config = new WebSocketConfig();
        
        // 测试边界值
        config.setConnectionTimeout(1);
        assertEquals(1, config.getConnectionTimeout());
        
        config.setMaxReconnectAttempts(0);
        assertEquals(0, config.getMaxReconnectAttempts());
        
        config.setInitialReconnectDelay(100);
        assertEquals(100, config.getInitialReconnectDelay());
        
        config.setMaxReconnectDelay(1000);
        assertEquals(1000, config.getMaxReconnectDelay());
        
        config.setReconnectCooldownPeriod(0);
        assertEquals(0, config.getReconnectCooldownPeriod());
        
        config.setHeartbeatInterval(1);
        assertEquals(1, config.getHeartbeatInterval());
    }
}
