package org.dromara.sol.websocket.strategy;

import org.dromara.sol.websocket.config.WebSocketConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 重连策略测试
 */
@ExtendWith(MockitoExtension.class)
class ReconnectStrategyTest {

    @Mock
    private WebSocketConfig config;

    private ReconnectStrategy reconnectStrategy;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(config.isAutoReconnect()).thenReturn(true);
        when(config.getMaxReconnectAttempts()).thenReturn(3);
        when(config.getInitialReconnectDelay()).thenReturn(100L);
        when(config.getMaxReconnectDelay()).thenReturn(1000L);
        when(config.getReconnectCooldownPeriod()).thenReturn(5000L);

        reconnectStrategy = new ReconnectStrategy(config);
    }

    @Test
    void testScheduleReconnectWhenAutoReconnectDisabled() {
        // 禁用自动重连
        when(config.isAutoReconnect()).thenReturn(false);
        
        AtomicInteger callCount = new AtomicInteger(0);
        Runnable task = callCount::incrementAndGet;

        boolean result = reconnectStrategy.scheduleReconnect(task);

        assertFalse(result);
        assertEquals(0, callCount.get());
    }

    @Test
    void testScheduleReconnectSuccess() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicInteger callCount = new AtomicInteger(0);
        
        Runnable task = () -> {
            callCount.incrementAndGet();
            latch.countDown();
        };

        boolean result = reconnectStrategy.scheduleReconnect(task);

        assertTrue(result);
        assertTrue(latch.await(2, TimeUnit.SECONDS));
        assertEquals(1, callCount.get());
        assertEquals(1, reconnectStrategy.getCurrentAttemptCount());
    }

    @Test
    void testMaxReconnectAttemptsReached() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(3);
        AtomicInteger callCount = new AtomicInteger(0);
        
        Runnable task = () -> {
            callCount.incrementAndGet();
            latch.countDown();
        };

        // 执行3次重连
        for (int i = 0; i < 3; i++) {
            reconnectStrategy.scheduleReconnect(task);
        }

        assertTrue(latch.await(3, TimeUnit.SECONDS));
        assertEquals(3, callCount.get());
        assertEquals(3, reconnectStrategy.getCurrentAttemptCount());

        // 第4次重连应该进入冷却期
        CountDownLatch cooldownLatch = new CountDownLatch(1);
        AtomicInteger cooldownCallCount = new AtomicInteger(0);
        
        Runnable cooldownTask = () -> {
            cooldownCallCount.incrementAndGet();
            cooldownLatch.countDown();
        };

        boolean result = reconnectStrategy.scheduleReconnect(cooldownTask);
        assertTrue(result); // 应该安排冷却期后的重连

        // 在短时间内不应该执行
        assertFalse(cooldownLatch.await(500, TimeUnit.MILLISECONDS));
        assertEquals(0, cooldownCallCount.get());
    }

    @Test
    void testResetAttemptCount() {
        // 先增加一些重连次数
        reconnectStrategy.scheduleReconnect(() -> {});
        assertTrue(reconnectStrategy.getCurrentAttemptCount() > 0);

        // 重置计数器
        reconnectStrategy.resetAttemptCount();
        assertEquals(0, reconnectStrategy.getCurrentAttemptCount());
    }

    @Test
    void testDelayCalculation() throws InterruptedException {
        CountDownLatch latch1 = new CountDownLatch(1);
        CountDownLatch latch2 = new CountDownLatch(1);
        
        long startTime1 = System.currentTimeMillis();
        reconnectStrategy.scheduleReconnect(() -> {
            long endTime1 = System.currentTimeMillis();
            long delay1 = endTime1 - startTime1;
            assertTrue(delay1 >= 100); // 第一次延迟应该至少100ms
            latch1.countDown();
        });

        assertTrue(latch1.await(1, TimeUnit.SECONDS));

        long startTime2 = System.currentTimeMillis();
        reconnectStrategy.scheduleReconnect(() -> {
            long endTime2 = System.currentTimeMillis();
            long delay2 = endTime2 - startTime2;
            assertTrue(delay2 >= 200); // 第二次延迟应该至少200ms（指数退避）
            latch2.countDown();
        });

        assertTrue(latch2.await(1, TimeUnit.SECONDS));
    }

    @Test
    void testShutdown() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        
        // 安排一个延迟任务
        reconnectStrategy.scheduleReconnect(() -> latch.countDown());
        
        // 立即关闭
        reconnectStrategy.shutdown();
        
        // 任务不应该执行
        assertFalse(latch.await(500, TimeUnit.MILLISECONDS));
    }

    @Test
    void testExceptionInReconnectTask() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        
        Runnable faultyTask = () -> {
            latch.countDown();
            throw new RuntimeException("测试异常");
        };

        boolean result = reconnectStrategy.scheduleReconnect(faultyTask);
        
        assertTrue(result);
        assertTrue(latch.await(1, TimeUnit.SECONDS));
        // 即使任务抛出异常，重连策略也应该继续工作
        assertEquals(1, reconnectStrategy.getCurrentAttemptCount());
    }
}
