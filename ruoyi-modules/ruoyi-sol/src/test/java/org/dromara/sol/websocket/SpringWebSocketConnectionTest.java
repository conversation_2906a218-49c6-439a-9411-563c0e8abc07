package org.dromara.sol.websocket;

import lombok.extern.slf4j.Slf4j;
import org.dromara.sol.websocket.impl.SpringWebSocketClientAdapter;
import org.junit.jupiter.api.Test;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;

import java.net.URI;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Spring WebSocket连接测试
 * 用于调试QuickNode WebSocket连接问题
 */
@Slf4j
public class SpringWebSocketConnectionTest {

    private static final String QUICKNODE_WSS_URL = "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/";
    private static final String ALTERNATIVE_WSS_URL = "wss://api.devnet.solana.com/";

    @Test
    public void testQuickNodeWebSocketConnection() {
        log.info("=== 测试QuickNode WebSocket连接 ===");
        testWebSocketConnection(QUICKNODE_WSS_URL, "QuickNode");
    }

    @Test
    public void testAlternativeWebSocketConnection() {
        log.info("=== 测试备用WebSocket连接 ===");
        testWebSocketConnection(ALTERNATIVE_WSS_URL, "Solana官方");
    }

    private void testWebSocketConnection(String wsUrl, String provider) {
        SpringWebSocketClientAdapter client = new SpringWebSocketClientAdapter();
        CountDownLatch connectionLatch = new CountDownLatch(1);
        AtomicBoolean connected = new AtomicBoolean(false);
        AtomicReference<String> errorMessage = new AtomicReference<>();

        // 设置回调函数
        client.setOnOpenCallback(session -> {
            log.info("✅ {} WebSocket连接成功建立！", provider);
            log.info("会话ID: {}", session.getId());
            log.info("远程地址: {}", session.getRemoteAddress());
            log.info("协议版本: {}", session.getHandshakeInfo().getSubProtocol());
            connected.set(true);
            connectionLatch.countDown();
        });

        client.setOnErrorCallback(throwable -> {
            log.error("❌ {} WebSocket连接错误: {}", provider, throwable.getMessage(), throwable);
            errorMessage.set(throwable.getMessage());
            connectionLatch.countDown();
        });

        client.setOnCloseCallback(closeStatus -> {
            log.info("🔌 {} WebSocket连接关闭: [{}] {}", provider, closeStatus.getCode(), closeStatus.getReason());
        });

        client.setOnMessageCallback(message -> {
            log.info("📨 收到{}消息: {}", provider, message.length() > 100 ? 
                message.substring(0, 100) + "..." : message);
        });

        try {
            log.info("🔄 正在连接到 {} WebSocket: {}", provider, wsUrl);
            URI uri = new URI(wsUrl);
            
            // 记录连接详情
            log.info("URI详情:");
            log.info("  - Scheme: {}", uri.getScheme());
            log.info("  - Host: {}", uri.getHost());
            log.info("  - Port: {}", uri.getPort());
            log.info("  - Path: {}", uri.getPath());

            client.connect(uri);

            // 等待连接结果
            boolean connectionResult = connectionLatch.await(30, TimeUnit.SECONDS);

            if (connectionResult && connected.get()) {
                log.info("✅ {} 连接测试成功！", provider);
                
                // 测试发送消息
                testSendMessage(client, provider);
                
                // 等待一段时间接收消息
                Thread.sleep(5000);
                
            } else {
                log.error("❌ {} 连接测试失败！", provider);
                if (errorMessage.get() != null) {
                    log.error("错误信息: {}", errorMessage.get());
                } else {
                    log.error("连接超时 - 30秒内未能建立连接");
                }
                
                // 提供故障排除建议
                provideTroubleshootingAdvice(provider, wsUrl);
            }

        } catch (Exception e) {
            log.error("❌ {} 连接测试异常: {}", provider, e.getMessage(), e);
        } finally {
            // 清理连接
            try {
                client.close();
                log.info("🧹 {} 连接已清理", provider);
            } catch (Exception e) {
                log.warn("清理连接时发生错误", e);
            }
        }
    }

    private void testSendMessage(SpringWebSocketClientAdapter client, String provider) {
        try {
            log.info("📤 测试发送消息到 {}", provider);
            
            // 发送一个简单的Solana RPC请求
            String testMessage = """
                {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                """;
            
            boolean sent = client.sendMessage(testMessage);
            if (sent) {
                log.info("✅ 消息发送成功到 {}", provider);
            } else {
                log.warn("⚠️ 消息发送失败到 {}", provider);
            }
            
        } catch (Exception e) {
            log.error("发送测试消息时发生错误", e);
        }
    }

    private void provideTroubleshootingAdvice(String provider, String wsUrl) {
        log.info("🔧 {} 故障排除建议:", provider);
        log.info("1. 检查网络连接");
        log.info("2. 验证WebSocket URL: {}", wsUrl);
        log.info("3. 检查防火墙设置");
        log.info("4. 验证SSL/TLS证书");
        
        if (provider.contains("QuickNode")) {
            log.info("5. 检查QuickNode仪表板中的端点状态");
            log.info("6. 验证QuickNode账户是否有效");
            log.info("7. 检查是否需要API密钥认证");
        }
        
        log.info("8. 尝试使用curl测试连接:");
        log.info("   curl -i -N -H \"Connection: Upgrade\" -H \"Upgrade: websocket\" \\");
        log.info("        -H \"Sec-WebSocket-Version: 13\" -H \"Sec-WebSocket-Key: test\" \\");
        log.info("        {}", wsUrl.replace("wss://", "https://"));
    }

    /**
     * 测试多个WebSocket URL
     */
    @Test
    public void testMultipleWebSocketUrls() {
        String[] testUrls = {
            "wss://aged-chaotic-telescope.solana-devnet.quiknode.pro/",
            "wss://api.devnet.solana.com/",
            "wss://solana-devnet.g.alchemy.com/v2/demo/"
        };

        for (String url : testUrls) {
            log.info("\n" + "=".repeat(60));
            log.info("测试URL: {}", url);
            log.info("=".repeat(60));
            
            try {
                testWebSocketConnection(url, extractProviderName(url));
                Thread.sleep(2000); // 等待2秒再测试下一个
            } catch (Exception e) {
                log.error("测试URL {} 时发生异常: {}", url, e.getMessage());
            }
        }
    }

    private String extractProviderName(String url) {
        if (url.contains("quiknode")) return "QuickNode";
        if (url.contains("solana.com")) return "Solana官方";
        if (url.contains("alchemy")) return "Alchemy";
        return "未知提供商";
    }
}
