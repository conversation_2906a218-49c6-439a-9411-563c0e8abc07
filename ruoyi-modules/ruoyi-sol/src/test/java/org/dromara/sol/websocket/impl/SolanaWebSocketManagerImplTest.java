package org.dromara.sol.websocket.impl;

import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.websocket.config.WebSocketConfig;
import org.dromara.sol.websocket.strategy.ReconnectStrategy;
import org.java_websocket.handshake.ServerHandshake;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Solana WebSocket管理器测试
 */
@ExtendWith(MockitoExtension.class)
class SolanaWebSocketManagerImplTest {

    @Mock
    private SolRpcConfig rpcConfig;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private WebSocketConfig webSocketConfig;

    @Mock
    private ReconnectStrategy reconnectStrategy;

    private SolanaWebSocketManagerImpl webSocketManager;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(rpcConfig.getWebsocketUrl()).thenReturn("ws://echo.websocket.org/");
        when(webSocketConfig.getConnectionTimeout()).thenReturn(5);
        when(webSocketConfig.getHeartbeatInterval()).thenReturn(30);
        when(webSocketConfig.isAutoReconnect()).thenReturn(true);
        when(webSocketConfig.isHeartbeatEnabled()).thenReturn(false); // 禁用心跳以简化测试

        webSocketManager = new SolanaWebSocketManagerImpl(rpcConfig, eventPublisher, webSocketConfig, reconnectStrategy);
    }

    @Test
    void testInitialization() {
        assertNotNull(webSocketManager);
        assertFalse(webSocketManager.isConnected());
    }

    @Test
    void testInitializeWebSocketClientWithValidUrl() {
        assertDoesNotThrow(() -> webSocketManager.initializeWebSocketClient());
        
        // 验证重连策略被调用
        verify(reconnectStrategy, timeout(1000).atLeastOnce()).scheduleReconnect(any(Runnable.class));
    }

    @Test
    void testInitializeWebSocketClientWithInvalidUrl() {
        when(rpcConfig.getWebsocketUrl()).thenReturn("");
        
        assertDoesNotThrow(() -> webSocketManager.initializeWebSocketClient());
        
        // 应该不会尝试连接
        verify(reconnectStrategy, never()).scheduleReconnect(any(Runnable.class));
    }

    @Test
    void testInitializeWebSocketClientWithNullUrl() {
        when(rpcConfig.getWebsocketUrl()).thenReturn(null);
        
        assertDoesNotThrow(() -> webSocketManager.initializeWebSocketClient());
        
        // 应该不会尝试连接
        verify(reconnectStrategy, never()).scheduleReconnect(any(Runnable.class));
    }

    @Test
    void testSendMessageWhenNotConnected() {
        boolean result = webSocketManager.sendMessage("test message");
        assertFalse(result);
    }

    @Test
    void testCloseConnection() {
        assertDoesNotThrow(() -> webSocketManager.closeConnection());
    }

    @Test
    void testSetCallbacks() {
        AtomicBoolean openCalled = new AtomicBoolean(false);
        AtomicBoolean messageCalled = new AtomicBoolean(false);
        AtomicBoolean closeCalled = new AtomicBoolean(false);

        assertDoesNotThrow(() -> {
            webSocketManager.setOnOpenCallback(handshake -> openCalled.set(true));
            webSocketManager.setOnMessageCallback(message -> messageCalled.set(true));
            webSocketManager.setOnCloseCallback(closeInfo -> closeCalled.set(true));
        });
    }

    @Test
    void testWaitForConnectionWhenNotInitialized() throws InterruptedException {
        boolean result = webSocketManager.waitForConnection(1, TimeUnit.SECONDS);
        assertFalse(result);
    }

    @Test
    void testCleanup() {
        // 初始化连接
        webSocketManager.initializeWebSocketClient();
        
        // 清理应该不抛出异常
        assertDoesNotThrow(() -> webSocketManager.cleanup());
        
        // 验证重连策略被关闭
        verify(reconnectStrategy).shutdown();
    }

    @Test
    void testHeartbeatEnabled() {
        when(webSocketConfig.isHeartbeatEnabled()).thenReturn(true);
        
        // 重新创建管理器以启用心跳
        webSocketManager = new SolanaWebSocketManagerImpl(rpcConfig, eventPublisher, webSocketConfig, reconnectStrategy);
        
        assertDoesNotThrow(() -> webSocketManager.init());
    }

    @Test
    void testCallbackExceptionHandling() {
        // 设置会抛出异常的回调
        webSocketManager.setOnOpenCallback(handshake -> {
            throw new RuntimeException("测试异常");
        });

        webSocketManager.setOnMessageCallback(message -> {
            throw new RuntimeException("测试异常");
        });

        webSocketManager.setOnCloseCallback(closeInfo -> {
            throw new RuntimeException("测试异常");
        });

        // 初始化不应该因为回调异常而失败
        assertDoesNotThrow(() -> webSocketManager.initializeWebSocketClient());
    }

    @Test
    void testConcurrentOperations() throws InterruptedException {
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicBoolean hasException = new AtomicBoolean(false);

        // 并发执行多个操作
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    webSocketManager.isConnected();
                    webSocketManager.sendMessage("test");
                    webSocketManager.closeConnection();
                } catch (Exception e) {
                    hasException.set(true);
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        assertTrue(latch.await(5, TimeUnit.SECONDS));
        assertFalse(hasException.get(), "并发操作不应该产生异常");
    }

    @Test
    void testReconnectOnConnectionFailure() {
        // 模拟连接失败
        when(rpcConfig.getWebsocketUrl()).thenReturn("ws://invalid-host.com/");
        
        webSocketManager.initializeWebSocketClient();
        
        // 验证重连策略被调用
        verify(reconnectStrategy, timeout(2000).atLeastOnce()).scheduleReconnect(any(Runnable.class));
    }

    @Test
    void testEventPublishing() {
        // 初始化连接
        webSocketManager.initializeWebSocketClient();
        
        // 设置消息回调来验证事件发布
        AtomicReference<String> receivedMessage = new AtomicReference<>();
        webSocketManager.setOnMessageCallback(message -> {
            receivedMessage.set(message);
        });

        // 由于这是单元测试，我们无法真正接收WebSocket消息
        // 但可以验证回调设置正确
        assertNotNull(receivedMessage);
    }

    @Test
    void testMultipleInitializationCalls() {
        // 多次初始化应该是安全的
        assertDoesNotThrow(() -> {
            webSocketManager.initializeWebSocketClient();
            webSocketManager.initializeWebSocketClient();
            webSocketManager.initializeWebSocketClient();
        });
    }

    @Test
    void testStateConsistency() {
        // 初始状态
        assertFalse(webSocketManager.isConnected());

        // 初始化后状态
        webSocketManager.initializeWebSocketClient();
        
        // 关闭后状态
        webSocketManager.closeConnection();
        assertFalse(webSocketManager.isConnected());
    }
}
